# Python cache
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE and editors
.idea/
.vscode/
*.swp
*.swo
*~
.cursor/

# Jupyter Notebook
.ipynb_checkpoints

# PyTorch models and data
*.pth
*.pt
*.pkl
*.joblib
checkpoints/
results/
logs/
*.log

# Data files (optional - comment out if you want to track data)
data/raw/*.hdf5
data/processed/*.pt
data/processed/*.joblib

# OS specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Task Master files
.taskmaster/
.claude/
.roomodes
.clinerules/
.trae/
.windsurf/
tests/
.env.example
.mcp.json

# GitHub/Agent specific
AGENTS.md
CLAUDE.md

# Test artifacts
.pytest_cache/
.coverage
htmlcov/

# Temporary files
*.tmp
*.temp
a.html 
.mcp.json

# Logs
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Task files
# tasks.json
# tasks/ 