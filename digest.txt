Directory structure:
└── TEC-MoLLM/
    ├── README.md
    ├── find_lr.py
    ├── TEC-MoLLM-PRD.md
    ├── test.py
    ├── train.py
    ├── experiments/
    │   └── l_in_study/
    │       ├── experiment_results.csv
    │       └── revised_experiment_plan.md
    ├── plans/
    │   ├── corect_revised.md
    │   ├── h20_optimization.prd
    │   └── revised.md
    ├── scripts/
    │   └── preprocess.py
    └── src/
        ├── data/
        │   ├── data_loader.py
        │   └── dataset.py
        ├── evaluation/
        │   └── metrics.py
        ├── features/
        │   └── feature_engineering.py
        ├── graph/
        │   └── graph_constructor.py
        ├── model/
        │   ├── modules.py
        │   └── tec_mollm.py
        └── models/
            └── baselines.py

================================================
FILE: README.md
================================================
# TEC-MoLLM: Total Electron Content Prediction using Multi-modal Large Language Model

A hybrid deep learning model for predicting Total Electron Content (TEC) in the ionosphere, combining Graph Neural Networks, Temporal Convolutional Networks, and Large Language Models with LoRA fine-tuning.

## 🌟 Features

- **Multi-modal Architecture**: Integrates spatial, temporal, and contextual features
- **Graph Neural Networks**: Captures geographical relationships using GATv2
- **Temporal Modeling**: Multi-scale convolutional embedding inspired by SeisMoLLM
- **LLM Integration**: GPT-2 backbone with LoRA for parameter-efficient fine-tuning
- **Comprehensive Evaluation**: Multiple metrics including MAE, RMSE, R², and Pearson correlation

## 🏗️ Architecture

```
Input Features (TEC + Space Weather) 
    ↓
Spatio-Temporal Embedding
    ↓
Spatial Encoder (GATv2)
    ↓
Temporal Encoder (Multi-scale Conv + Patching)
    ↓
LLM Backbone (GPT-2 + LoRA)
    ↓
Prediction Head
    ↓
TEC Predictions (12 horizons)
```

## 📊 Dataset

- **Data Source**: CRIM_SW2hr_AI ionosphere data (2014-2015)
- **Features**: 
  - TEC values (41×71 spatial grid)
  - Space weather indices (5 parameters)
  - Temporal coordinates
- **Splits**: 
  - Training: 2014 (4,368 samples)
  - Validation: Jan-Jun 2015 (2,160 samples) 
  - Test: Jul-Dec 2015 (2,196 samples)

## 🚀 Quick Start

### Prerequisites

```bash
# Core dependencies
pip install torch torch-geometric transformers peft einops
pip install scikit-learn scipy h5py joblib tqdm pandas

# For distributed training
pip install torch torchrun
```

### 1. Data Preparation

First, build the spatial graph:

```bash
python src/graph/graph_constructor.py
```

This creates a spatial adjacency matrix with 2,911 nodes (41×71 grid) and 20,924 edges using a 150km distance threshold.

### 2. Training

#### Single GPU Training

```bash
python train.py --epochs 50 --batch_size 4 --L_in 48
```

#### Distributed Training (Recommended for 2x RTX 3090)

```bash
# Launch distributed training on 2 GPUs
torchrun --nproc_per_node 2 train.py --epochs 50 --batch_size 2 --L_in 48

# Monitor training progress - detailed metrics every 10 epochs
# Epoch 10/50: Detailed metrics with per-horizon breakdown
# Epoch 20/50: Comprehensive performance analysis
# ...
```

**Training Parameters:**
- `--epochs`: Number of training epochs (default: 50)
- `--batch_size`: Batch size per GPU (default: 4, adjust based on GPU memory)
- `--L_in`: Input sequence length (default: 48 for memory efficiency)
- `--L_out`: Prediction horizon (default: 12)
- `--lr`: Learning rate (default: 1e-4)

### 3. Model Evaluation

```bash
# Evaluate trained model against baselines
python test.py --model_checkpoint checkpoints/best_model.pth --L_in 48

# Results will be saved to:
# - results/evaluation_results.csv (detailed metrics)
# - results/evaluation_summary.txt (summary report)
```

### 4. Training Monitoring

The training script provides comprehensive monitoring:

- **Every Epoch**: Basic train/validation loss and key metrics
- **Every 10 Epochs**: Detailed breakdown with:
  - Per-horizon MAE, RMSE, R², Pearson R values
  - Visual formatting with 80-character dividers
  - Best model checkpointing indicators

**Sample Output:**
```
================================================================================
DETAILED METRICS - Epoch 20/50
================================================================================
Training Loss: 45.234567
Validation Loss: 52.876543
Validation Metrics by Horizon:
  - MAE Average: 156.789012
  - RMSE Average: 223.456789
  - R² Score Average: 0.678901
  - Pearson R Average: 0.834567

MAE by Horizon:
  Horizon  1: 145.123456
  Horizon  2: 152.789012
  ...
  Horizon 12: 167.890123
================================================================================
```

### Project Structure

```
TEC-MoLLM/
├── src/
│   ├── data/
│   │   ├── data_loader.py      # HDF5 data loading
│   │   └── dataset.py          # PyTorch sliding window dataset
│   ├── features/
│   │   └── feature_engineering.py  # Feature preprocessing
│   ├── graph/
│   │   └── graph_constructor.py     # Spatial graph construction
│   ├── model/
│   │   ├── modules.py               # Core model components
│   │   └── tec_mollm.py            # Main model assembly
│   ├── evaluation/
│   │   └── metrics.py              # Evaluation metrics
│   └── models/
│       └── baselines.py            # Baseline models (HA, SARIMA)
├── tests/                          # Unit tests
├── train.py                       # Training script
├── test.py                        # Testing script
└── data/
    ├── raw/                       # Original HDF5 files
    └── processed/                 # Preprocessed data
```

## 📈 Results

### Current Performance (20 epochs, distributed training on 2x RTX 3090)

| Metric | Value |
|--------|-------|
| MAE | ~150-170 TECU |
| RMSE | ~220-250 TECU |
| R² | 0.67+ |
| Pearson R | 0.84+ |

### Training Progress

The distributed training framework shows excellent scalability and performance:

- **Distributed Setup**: Successfully implemented PyTorch DDP for 2x RTX 3090 GPUs
- **Memory Optimization**: L_in=48, batch_size=2 per GPU for stable training
- **Monitoring Enhancement**: Detailed metrics every 10 epochs with per-horizon breakdown
- **Performance Trajectory**: Consistent improvement from R² ~0.41 to 0.67+ over 20 epochs

### Key Improvements Implemented

- ✅ **Distributed Training**: Multi-GPU support with DistributedDataParallel
- ✅ **Enhanced Monitoring**: Comprehensive metrics logging with visual formatting
- ✅ **Memory Efficiency**: Optimized parameters for RTX 3090 hardware
- ✅ **Real-time Features**: Proper time feature extraction and propagation
- ✅ **Model Checkpointing**: Best model saving with DDP compatibility

## 🔧 Model Components

### 1. Spatio-Temporal Embedding
- Node embeddings for 2,911 grid points
- Time-of-day and day-of-year embeddings
- Learnable 16-dimensional representations

### 2. Spatial Encoder (GATv2)
- Graph attention mechanism for spatial dependencies
- Edge connectivity based on geographical distance
- Multi-head attention (2 heads, 32 output channels)

### 3. Temporal Encoder
- Multi-scale convolutional blocks (kernel sizes: 3, 5, 7)
- Downsampling with strides [2, 2]: 24 → 12 → 6 timesteps
- Latent patching for LLM compatibility

### 4. LLM Backbone
- GPT-2 model truncated to 3 layers
- LoRA adaptation (r=16, α=32)
- Parameter-efficient fine-tuning (1.55% trainable parameters)

### 5. Prediction Head
- Linear projection to 12 prediction horizons
- Maps from LLM hidden dimension to TEC forecasts

## 🎯 Development Status & Future Improvements

### ✅ Completed Features
- **Distributed Training**: Multi-GPU support with PyTorch DDP
- **Real-time Features**: Hour-of-day and day-of-year temporal embeddings
- **Enhanced Monitoring**: Detailed metrics logging with per-horizon breakdown
- **Model Evaluation**: Complete testing framework with baseline comparisons
- **Memory Optimization**: Efficient training on RTX 3090 hardware

### 🚧 In Progress
- [ ] Full 50-epoch training runs for optimal performance
- [ ] Advanced evaluation against more sophisticated baselines

### 🔮 Future Enhancements

#### High Priority
- [ ] Increase input window size to 72-168 timesteps for better temporal context
- [ ] Implement advanced loss functions (Huber, Focal loss)
- [ ] Learning rate scheduling and optimization
- [ ] Model pruning and quantization for deployment

#### Medium Priority
- [ ] Data augmentation strategies for robustness
- [ ] Deeper LLM architectures (6-12 layers)
- [ ] Ensemble methods for improved accuracy
- [ ] Real-time inference optimization

#### Low Priority
- [ ] Advanced attention mechanisms for spatio-temporal fusion
- [ ] Physics-informed constraints and regularization
- [ ] Hyperparameter optimization with Optuna/Ray Tune
- [ ] Multi-modal feature integration (solar wind, magnetic field)

## 📚 References

- **Graph Attention Networks**: Veličković et al. (2018)
- **LoRA**: Hu et al. (2021) - Low-Rank Adaptation of Large Language Models
- **SeisMoLLM**: Inspired by seismic modeling approaches
- **TEC Prediction**: Ionosphere research and space weather modeling

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📞 Contact

For questions and collaborations, please open an issue in this repository.

---

⭐ **Star this repository if you find it helpful!**


================================================
FILE: find_lr.py
================================================
import torch
import torch._dynamo
import torch.nn as nn
from torch.utils.data import DataLoader
from torch.utils.data.distributed import DistributedSampler
import torch.distributed as dist
import torch.multiprocessing as mp
import numpy as np
import logging
import os
import joblib
import argparse
from tqdm import tqdm
import matplotlib.pyplot as plt

from src.data.dataset import SlidingWindowSamplerDataset
from src.model.tec_mollm import TEC_MoLLM

# Suppress torch.compile backend errors and fallback to eager execution
torch._dynamo.config.suppress_errors = True

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def setup_distributed():
    """Initialize distributed training"""
    if 'RANK' in os.environ and 'WORLD_SIZE' in os.environ:
        rank = int(os.environ['RANK'])
        world_size = int(os.environ['WORLD_SIZE'])
        local_rank = int(os.environ['LOCAL_RANK'])
        
        dist.init_process_group(backend='nccl', rank=rank, world_size=world_size)
        torch.cuda.set_device(local_rank)
        
        return rank, world_size, local_rank
    else:
        return 0, 1, 0

def cleanup_distributed():
    """Clean up distributed training"""
    if dist.is_initialized():
        dist.destroy_process_group()

def parse_args():
    """Parse command line arguments for LR finder."""
    parser = argparse.ArgumentParser(description='Learning Rate Range Test for TEC-MoLLM')
    
    # Data configuration (copied from train.py)
    parser.add_argument('--L_in', type=int, default=48, help='Input sequence length')
    parser.add_argument('--L_out', type=int, default=12, help='Output sequence length')
    
    # Model configuration (copied from train.py)
    parser.add_argument('--d_emb', type=int, default=16, help='Embedding dimension')
    parser.add_argument('--llm_layers', type=int, default=3, help='Number of LLM layers')

    # LR Finder configuration
    parser.add_argument('--min_lr', type=float, default=1e-8, help='Minimum learning rate')
    parser.add_argument('--max_lr', type=float, default=1e-1, help='Maximum learning rate')
    parser.add_argument('--num_iter', type=int, default=100, help='Number of iterations for the test')
    parser.add_argument('--batch_size', type=int, default=16, help='Batch size')
    parser.add_argument('--output_dir', type=str, default='results', help='Directory to save results')

    return parser.parse_args()

def find_lr(args):
    """Main function to run the LR range test."""
    rank, world_size, local_rank = setup_distributed()
    device = torch.device(f"cuda:{local_rank}" if torch.cuda.is_available() else "cpu")
    
    if rank == 0:
        logging.info("--- Starting Learning Rate Range Test ---")
        logging.info(f"Using device: {device}, Rank: {rank}/{world_size}")
        logging.info(f"Test Configuration: min_lr={args.min_lr}, max_lr={args.max_lr}, num_iter={args.num_iter}")

    # --- Setup paths and model config (same as train.py) ---
    data_dir = 'data/processed'
    graph_path = os.path.join(data_dir, 'graph_A.pt')
    target_scaler_path = os.path.join(data_dir, 'target_scaler.joblib')
    os.makedirs(args.output_dir, exist_ok=True)

    conv_output_len = args.L_in // 4
    patch_len = 4 if conv_output_len % 4 == 0 else (2 if conv_output_len % 2 == 0 else 1)
    
    model_config = {
        "num_nodes": 2911, "d_emb": args.d_emb, "spatial_in_channels_base": 6,
        "spatial_out_channels": 32, "spatial_heads": 2, "temporal_channel_list": [64, 128],
        "temporal_strides": [2, 2], "patch_len": patch_len, "d_llm": 768, "llm_layers": args.llm_layers,
        "prediction_horizon": args.L_out, "temporal_seq_len": args.L_in
    }
    
    # --- Load Data (same as train.py) ---
    if rank == 0: logging.info("Loading data...")
    try:
        target_scaler = joblib.load(target_scaler_path)
    except FileNotFoundError:
        logging.error(f"Target scaler not found at {target_scaler_path}.")
        cleanup_distributed()
        return

    train_dataset = SlidingWindowSamplerDataset(data_path=data_dir, mode='train', L_in=args.L_in, L_out=args.L_out, target_scaler=target_scaler)
    edge_index = torch.load(graph_path)['edge_index'].to(device)

    train_sampler = DistributedSampler(train_dataset, num_replicas=world_size, rank=rank, shuffle=True) if world_size > 1 else None
    
    train_loader = DataLoader(
        train_dataset, 
        batch_size=args.batch_size, 
        shuffle=(train_sampler is None), 
        sampler=train_sampler,
        num_workers=4, pin_memory=True, prefetch_factor=4
    )
    
    # --- Setup Model, Optimizer, Loss (same as train.py but optimizer LR is placeholder) ---
    model = TEC_MoLLM(model_config).to(device)
    model = torch.compile(model)
    if world_size > 1:
        model = nn.parallel.DistributedDataParallel(model, device_ids=[local_rank])
    
    optimizer = torch.optim.AdamW(filter(lambda p: p.requires_grad, model.parameters()), lr=args.min_lr)
    loss_fn = nn.MSELoss()
    scaler = torch.cuda.amp.GradScaler()

    # --- LR Range Test Logic ---
    lrs = np.logspace(np.log10(args.min_lr), np.log10(args.max_lr), args.num_iter)
    losses = []
    
    model.train()
    data_iter = iter(train_loader)
    
    progress_bar = tqdm(range(args.num_iter), desc="Finding LR", disable=(rank!=0))
    for i in progress_bar:
        # Get next batch, reset iterator if needed
        try:
            batch = next(data_iter)
        except StopIteration:
            data_iter = iter(train_loader)
            batch = next(data_iter)

        # Update LR for this step
        for param_group in optimizer.param_groups:
            param_group['lr'] = lrs[i]

        # Forward and backward pass
        x = batch['x'].to(device)
        y = batch['y'].to(device)
        time_features = batch['x_time_features'].to(device)

        B, L, H, W, C = x.shape
        x = x.view(B, L, H * W, C)
        if time_features.numel() > 0:
            time_features = time_features.unsqueeze(-2).expand(B, L, H * W, -1)

        optimizer.zero_grad()
        with torch.autocast(device_type='cuda', dtype=torch.float16):
            output = model(x, time_features, edge_index)
            y_reshaped = y.permute(0, 3, 1, 2).reshape(B, -1, H * W, 1)
            loss = loss_fn(output, y_reshaped)
        
        scaler.scale(loss).backward()
        scaler.step(optimizer)
        scaler.update()

        losses.append(loss.item())
        progress_bar.set_postfix(lr=lrs[i], loss=loss.item())

        # Stop if loss explodes
        if loss.item() > 4 * (losses[0] if losses else 1.0) and i > 10:
            if rank == 0: logging.info("Loss exploded, stopping test.")
            break
    
    # Only rank 0 saves the plot
    if rank == 0:
        # Trim the lists if the loop broke early
        lrs = lrs[:len(losses)]
        
        logging.info("LR range test finished. Plotting results...")
        plt.figure()
        plt.plot(lrs, losses)
        plt.xscale('log')
        plt.xlabel('Learning Rate')
        plt.ylabel('Loss')
        plt.title('Learning Rate Range Test')
        
        plot_path = os.path.join(args.output_dir, 'lr_finder_plot.png')
        plt.savefig(plot_path)
        logging.info(f"Plot saved to {plot_path}")
        
        # Save raw data
        data_path = os.path.join(args.output_dir, 'lr_finder_data.npz')
        np.savez(data_path, lrs=lrs, losses=losses)
        logging.info(f"Raw data saved to {data_path}")

        # Suggest best LR
        try:
            # Find the point with the steepest gradient
            # We ignore the first few and last few points for stability
            skip_start = 10
            skip_end = 5
            if len(losses) > skip_start + skip_end:
                losses_smoothed = np.convolve(losses, np.ones(5)/5, mode='valid')
                lrs_smoothed = lrs[len(lrs) - len(losses_smoothed):]

                # Use numpy gradient on log-loss
                grads = np.gradient(np.log10(losses_smoothed))
                
                # Find the minimum gradient (steepest descent)
                best_idx = np.argmin(grads[skip_start:-skip_end]) + skip_start
                best_lr = lrs_smoothed[best_idx]
                logging.info(f"馃搱 Suggested LR (steepest descent): {best_lr:.2e}")
        except Exception as e:
            logging.warning(f"Could not automatically suggest a learning rate: {e}")


    cleanup_distributed()

if __name__ == '__main__':
    args = parse_args()
    find_lr(args) 


================================================
FILE: TEC-MoLLM-PRD.md
================================================
### **项目研究文档 (PRD): `TEC-MoLLM` 时空预测模型**

**文档版本**: 1.0
**项目代号**: `TEC-MoLLM`
**阶段**: v0.1 - 沙盒验证 (Sandbox Validation)
**目标**: 验证融合GNN与预训练LLM进行电离层TEC时空预测的技术可行性。
**环境**: : 已有conda环境 `tecgpt_env`，请在这个环境上修改

---
### **1. 数据集与预处理 (Dataset and Preprocessing)**

#### **1.1. 源数据描述**

*   **数据集**: `/home/<USER>/TEC-MoLLM/data/raw/CRIM_SW2hr_AI_v1.2_2014_DataDrivenRange_CN.hdf5和`/home/<USER>/TEC-MoLLM/data/raw/CRIM_SW2hr_AI_v1.2_2015_DataDrivenRange_CN.hdf5
*   **格式**: HDF5
*   **核心内容**:
    *   `ionosphere/TEC`: `(N_times, 41, 71)` - 垂直总电子含量 (TECU)
    *   `space_weather_indices`: 5个关键指数 (Kp, Dst, ap, F10.7, AE)，形状 `(N_times,)`
    *   `coordinates`: `latitude(41)`, `longitude(71)`, `time`, `hour`, `day_of_year` 等
*   **时间分辨率**: 2小时 (12个数据点/天)
*   **空间分辨率**: 1°x1° 格网 (41x71 = 2911个节点)

#### **1.2. 本阶段使用的数据子集**

*   **目的**: 用于快速原型开发和初步验证。
*   **时间范围**: **2014-01-01T00:00:00Z 至 2015-12-31T22:00:00Z**。
*   **数据划分 (严格按时序)**:
    *   **训练集 (Training Set)**: 2014年数据 (共 `365 * 12 = 4380` 个时间步)。
    *   **验证集 (Validation Set)**: 2015年1月-6月数据 (约 `181 * 12 = 2172` 个时间步)。
    *   **测试集 (Test Set)**: 2015年7月-12月数据 (约 `184 * 12 = 2208` 个时间步)。

#### **1.3. 特征工程 (Feature Engineering)**

1.  **输入特征 `X`**:
    *   **目标**: 构建一个多通道的时空张量。
    *   **步骤**:
        a. 读取 `ionosphere/TEC`，作为基础特征通道。
        b. 读取 `space_weather_indices` 下的5个指数。注意Kp_Index 需要scale_factor变化
        c. 将每个一维的指数时间序列（shape: `(N_times,)`）扩展为三维张量（shape: `(N_times, 41, 71)`），使其在空间维度上广播。
        d. 将TEC张量和5个广播后的指数张量在新的特征维度上进行堆叠 (`np.stack` 或 `torch.stack`)。
    *   **最终 `X` 形状**: `(N_times, 41, 71, 6)`，其中6为特征数 (1个TEC + 5个指数)。

2.  **目标 `Y`**:
    *   **目标**: 预测未来24小时（12个时间步）的TEC值。
    *   **形状**: `(N_times, 41, 71, 12)`。

3.  **时间编码特征**:
    *   从 `coordinates` 组中提取 `hour` 和 `day_of_year`。这些将作为输入，送入模型中的`Embedding`层。

4.  **空间图结构**:
    *   **节点 (Nodes)**: 2911个格点。
    *   **邻接矩阵 `A`**:
        a. 根据 `latitude` 和 `longitude` 计算所有节点对之间的地理距离（Haversine公式）。
        b. 定义一个距离阈值 `d_threshold`（例如150km）。
        c. 如果两节点间距离小于`d_threshold`，则在邻接矩阵中对应位置设为1，否则为0。
        d. 对邻接矩阵进行归一化处理（例如，对称归一化：`D^(-1/2) * A * D^(-1/2)`）。
        e. 该矩阵构建一次后，保存为文件供模型加载。

#### **1.4. 数据标准化与样本生成**

1.  **标准化 (Standardization)**:
    *   在**训练集**上计算输入特征 `X` 中每个通道（TEC和5个指数）的均值和标准差。
    *   使用该均值和标准差对整个数据集（训练、验证、测试）的 `X` 进行Z-Score标准化。
    *   保存该`scaler`对象，用于后续结果的逆标准化。

2.  **滑动窗口采样 (Sliding Window Sampling)**:
    *   **输入序列长度 `L_in`**: 336 (28天)
    *   **输出序列长度 `L_out`**: 12 (1天)
    *   在标准化后的 `X` 和 `Y` 上进行滑动，生成 `(X_sample, Y_sample)` 对。每个 `X_sample` 形状为 `(336, 41, 71, 6)`，`Y_sample` 形状为 `(12, 41, 71, 1)`。

---

### **2. 模型架构与实现 (Model Architecture & Implementation)**

#### **2.1. 模型总体设计 (`TEC-MoLLM`)**
本模型是一个融合了图神经网络、多尺度时间卷积和预训练语言模型的混合架构，用于处理和预测格网化时空序列数据。

#### **2.2. 模块化分解**

1.  **时空嵌入模块 (`SpatioTemporalEmbedding`)**:
    *   **功能**: 为输入数据提供时空上下文。
    *   **组件**:
        *   `NodeEmbedding`: `nn.Embedding(num_nodes=2911, embedding_dim=D_emb)`，为每个格点生成可学习的空间向量。
        *   `TimeOfDayEmbedding`: `nn.Embedding(num_embeddings=12, embedding_dim=D_emb)`，为一天中的12个时刻生成时间向量。
        *   `DayOfYearEmbedding`: `nn.Embedding(num_embeddings=366, embedding_dim=D_emb)`，为一年中的日期生成季节性/年度性向量。
    *   **输出**: 与输入数据融合的时空特征。

2.  **空间特征提取模块 (`SpatialEncoder`)**:
    *   **功能**: 在每个时间步上，利用图结构聚合空间信息。
    *   **组件**: `torch_geometric.nn.GATv2Conv` (Graph Attention Network v2)。
    *   **输入**: 节点特征 `(B*L_in, N_nodes, D_in)` 和 `edge_index`。
    *   **输出**: 经过空间信息交互后的节点特征 `(B*L_in, N_nodes, D_out)`。

3.  **时间特征提取模块 (`TemporalEncoder`)**:
    *   **功能**: 从长序列中提取多尺度的动态时序特征。**(此部分核心逻辑借鉴自SeisMoLLM)**
    *   **组件**:
        *   **多尺度卷积嵌入器 (`MultiScaleConvEmbedder`)**:
            *   采用堆叠的`Multi_Scale_Conv_Block`。
            *   **配置**: `conv_strides=[2, 2, 1, 1]`，实现4倍下采样，将长度`336`的序列压缩至`84`。
        *   **隐式分块 (`LatentPatching`)**:
            *   在长度为`84`的特征序列上操作。
            *   **配置**: `patch_size=4`，生成`21`个Tokens。
            *   每个Token的维度将被`rearrange`至`768`，以匹配GPT-2的输入。

4.  **序列推理模块 (`LLMBackbone`)**:
    *   **功能**: 对经过时空编码和特征提取的Token序列进行深度序列建模。
    *   **组件**:
        *   **GPT-2**: 使用`transformers`库加载预训练的`gpt2`模型，截取前3层。
        *   **PEFT策略**: 采用**LoRA (Low-Rank Adaptation)**。
            *   `peft.LoraConfig`配置: `r=16`, `lora_alpha=32`, `target_modules=['q_attn', 'c_attn']`。
            *   使用`peft.get_peft_model`包装GPT-2模型。
            *   **训练参数**: 冻结GPT原始权重，仅训练LoRA参数、LayerNorm层和位置编码。

5.  **预测头模块 (`PredictionHead`)**:
    *   **功能**: 将LLM输出的序列特征解码为最终的预测结果。
    *   **组件**: 一个或多个`nn.Linear`层。
    *   **输入**: LLM输出的 `(B*N_nodes, 21, 768)` 特征序列。
    *   **处理**: 将输入展平 (`flatten`)，然后通过线性层映射到`L_out=12`的维度。
    *   **输出**: 预测结果 `(B, N_nodes, 12)`，需`reshape`。

#### **2.3. 数据流 (Data Flow)**

```mermaid
graph TD
    A[Input Data<br/>(B, 336, N, 6)] --> B(Spatio-Temporal<br/>Embedding Fusion);
    C[Adjacency Matrix A] --> D{GNN Encoder};
    B --> D;
    D --> E[Temporal ConvEmbedder<br/>(SeisMoLLM Style)];
    E --> F(Latent Patching<br/>84 -> 21 Tokens);
    F --> G{GPT-2 Backbone<br/>(with LoRA)};
    G --> H[Prediction Head];
    H --> I[Output<br/>(B, N, 12)];
```

---

### **3. 基线模型库 (Baseline Model Zoo)**

本框架将支持以下可扩展的基线模型库，用于性能对比。

| 模型类别     | 模型名称   | 核心技术           | 备注                 |
| :----------- | :--------- | :----------------- | :------------------- |
| **经典时序** | `HA`       | Historical Average | 性能下限             |
|              | `SARIMA`   | Seasonal ARIMA     | 经典统计模型         |
| **新增模型** | *(可扩展)* | -                  | *(可添加其他新模型)* |

---

### **4. 评估指标 (Evaluation Metrics)**

将采用以下指标全面评估模型性能。所有指标均在逆标准化后的真实值和预测值上计算。

*   **`MAE` (Mean Absolute Error)**: `mean(|y_true - y_pred|)`。核心评估指标。
*   **`RMSE` (Root Mean Squared Error)**: `sqrt(mean((y_true - y_pred)^2))`。对大误差更敏感。
*   **`R^2` (Coefficient of Determination)**: R-squared score。衡量模型对数据方差的解释程度。
*   **`Correlation Coefficient` (Pearson r)**: `Cov(y_true, y_pred) / (std(y_true) * std(y_pred))`。衡量预测值与真实值的线性相关性。

评估将在每个预测时间步（horizon 1 to 12）上独立进行，并报告所有时间步的**平均值**。


================================================
FILE: test.py
================================================
import torch
from torch.utils.data import DataLoader
import numpy as np
import logging
import os
import pandas as pd
from tqdm import tqdm
import argparse
import joblib
from sklearn.preprocessing import StandardScaler

from src.data.dataset import SlidingWindowSamplerDataset
from src.model.tec_mollm import TEC_MoLLM
from src.evaluation.metrics import evaluate_horizons

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_tec_mollm_predictions(model, dataloader, device, edge_index):
    """获取TEC-MoLLM模型的预测结果"""
    model.eval()
    all_preds = []
    all_trues = []
    with torch.no_grad():
        for batch in tqdm(dataloader, desc="TEC-MoLLM Inference"):
            x = batch['x'].to(device)
            y = batch['y'].to(device)
            time_features = batch['x_time_features'].to(device)
            
            # 与train.py中validate函数相同的reshape逻辑
            B, L, H, W, C = x.shape
            x = x.view(B, L, H * W, C)
            # time_features shape should be (B, L, N, 2), so we need to expand the spatial dimension
            if time_features.numel() > 0:
                time_features = time_features.unsqueeze(-2).expand(B, L, H * W, -1)  # (B, L, N, 2)
            
            output = model(x, time_features, edge_index)
            # Reshape target to match output: (B, H, W, L_out) -> (B, L_out, H*W, 1)
            y_reshaped = y.permute(0, 3, 1, 2).reshape(B, -1, H * W, 1)
            
            all_preds.append(output.cpu().numpy())
            all_trues.append(y_reshaped.cpu().numpy())
    
    return np.concatenate(all_trues, axis=0), np.concatenate(all_preds, axis=0)

def get_baseline_predictions(test_dataset, L_in, L_out):
    """生成简单的基线预测"""
    logging.info("生成历史平均基线预测...")
    
    predictions = []
    
    # 遍历测试数据集样本
    for i in range(len(test_dataset)):
        sample = test_dataset[i]
        x_window = sample['x'].numpy()  # (L_in, H, W, C)
        
        # 计算历史平均值 (对时间维度求平均)
        # 只使用TEC特征（第0个特征）进行预测
        historical_avg = np.mean(x_window[:, :, :, 0:1], axis=0, keepdims=True)  # (1, H, W, 1)
        
        # 重复L_out次作为未来预测
        pred = np.repeat(historical_avg, L_out, axis=0)  # (L_out, H, W, 1)
        
        # 转换为期望的格式 (H, W, L_out)
        pred_reshaped = pred.transpose(1, 2, 0, 3).squeeze(-1)  # (H, W, L_out)
        predictions.append(pred_reshaped)
    
    # 转换为最终格式 (num_samples, H, W, L_out)
    predictions = np.array(predictions)
    
    return predictions

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="评估TEC-MoLLM和基线模型")
    
    # 数据配置
    parser.add_argument('--L_in', type=int, default=48, help='输入序列长度')
    parser.add_argument('--L_out', type=int, default=12, help='输出序列长度')
    
    # 模型配置
    parser.add_argument('--d_emb', type=int, default=16, help='嵌入维度')
    parser.add_argument('--llm_layers', type=int, default=3, help='LLM层数')
    
    # 文件路径
    parser.add_argument('--target_scaler_path', type=str, default='data/processed/target_scaler.joblib')
    parser.add_argument('--graph_path', type=str, default='data/processed/graph_A.pt')
    parser.add_argument('--model_checkpoint', type=str, default='checkpoints/best_model.pth')
    parser.add_argument('--output_dir', type=str, default='results')
    parser.add_argument('--batch_size', type=int, default=16)
    
    return parser.parse_args()

def main():
    args = parse_args()
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    logging.info(f"使用设备: {device}")
    logging.info(f"测试配置: L_in={args.L_in}, L_out={args.L_out}")
    
    # --- 验证预处理数据文件存在 ---
    data_dir = 'data/processed'
    if not os.path.exists(args.target_scaler_path):
        logging.error(f"Target scaler not found at {args.target_scaler_path}. Please run preprocessing script first.")
        return
    
    logging.info("加载预处理好的测试数据...")
    
    # 创建测试数据集（使用预处理好的数据）
    test_dataset = SlidingWindowSamplerDataset(
        data_path=data_dir, 
        mode='test',
        L_in=args.L_in, 
        L_out=args.L_out
    )
    test_loader = DataLoader(test_dataset, batch_size=args.batch_size, shuffle=False)
    
    logging.info(f"测试数据集大小: {len(test_dataset)} 样本")
    
    # --- 模型配置 ---
    # 计算卷积后的时间序列长度
    conv_output_len = args.L_in // (2 * 2)  # 两个stride-2的卷积
    patch_len = 4
    num_patches = conv_output_len // patch_len
    
    if conv_output_len % patch_len != 0:
        patch_len = 2 if conv_output_len % 2 == 0 else 1
        num_patches = conv_output_len // patch_len
        logging.warning(f"调整patch_len为{patch_len}以适应conv_output_len {conv_output_len}")
    
    model_config = {
        "num_nodes": 2911, "d_emb": args.d_emb, "spatial_in_channels_base": 6,
        "spatial_out_channels": 32, "spatial_heads": 2, "temporal_channel_list": [64, 128],
        "temporal_strides": [2, 2], "patch_len": patch_len, "d_llm": 768, 
        "llm_layers": args.llm_layers, "prediction_horizon": args.L_out, 
        "temporal_seq_len": args.L_in
    }
    
    # --- 加载模型和获取预测 ---
    results = {}
    
    # TEC-MoLLM预测
    logging.info("加载TEC-MoLLM模型...")
    model = TEC_MoLLM(model_config).to(device)
    
    # 加载模型权重（处理DDP保存的模型）
    checkpoint = torch.load(args.model_checkpoint, map_location=device)
    if isinstance(checkpoint, dict) and 'module.' in list(checkpoint.keys())[0]:
        # 如果是DDP保存的模型，去除'module.'前缀
        new_checkpoint = {}
        for k, v in checkpoint.items():
            new_checkpoint[k.replace('module.', '')] = v
        checkpoint = new_checkpoint
    
    model.load_state_dict(checkpoint)
    edge_index = torch.load(args.graph_path)['edge_index'].to(device)
    
    logging.info("获取TEC-MoLLM预测结果...")
    y_true, y_pred_mollm = get_tec_mollm_predictions(model, test_loader, device, edge_index)
    
    # 基线预测
    logging.info("生成基线预测...")
    y_pred_ha = get_baseline_predictions(test_dataset, args.L_in, args.L_out)
    
    # 需要将基线预测重塑为与y_true相同的格式
    # y_true: (num_samples, L_out, H*W, 1)
    # y_pred_ha: (num_samples, H, W, L_out)
    B, H, W, L_out = y_pred_ha.shape
    y_pred_ha_reshaped = y_pred_ha.transpose(0, 3, 1, 2).reshape(B, L_out, H*W, 1)
    
    # 确保基线预测的样本数与真实值匹配
    min_samples = min(len(y_true), len(y_pred_ha_reshaped))
    y_true_matched = y_true[:min_samples]
    y_pred_ha_matched = y_pred_ha_reshaped[:min_samples]
    
    # --- 评估指标 ---
    logging.info("评估TEC-MoLLM模型...")
    results['TEC-MoLLM'] = evaluate_horizons(y_true_matched, y_pred_mollm[:min_samples], args.target_scaler_path)
    
    logging.info("评估历史平均基线...")
    results['HistoricalAverage'] = evaluate_horizons(y_true_matched, y_pred_ha_matched, args.target_scaler_path)

    # --- 格式化和保存结果 ---
    results_df = pd.DataFrame(results).T
    
    # 添加详细的结果展示
    logging.info("="*80)
    logging.info("最终评估结果")
    logging.info("="*80)
    
    for model_name, metrics in results.items():
        logging.info(f"\n📊 {model_name} 模型结果:")
        logging.info(f"  平均MAE:  {metrics['mae_avg']:.6f}")
        logging.info(f"  平均RMSE: {metrics['rmse_avg']:.6f}")
        logging.info(f"  平均R²:   {metrics['r2_score_avg']:.6f}")
        logging.info(f"  平均Pearson R: {metrics['pearson_r_avg']:.6f}")
        
        if 'mae_by_horizon' in metrics:
            logging.info("  各预测时步详细指标:")
            for h in range(len(metrics['mae_by_horizon'])):
                logging.info(f"    时步{h+1:2d}: MAE={metrics['mae_by_horizon'][h]:.6f}, "
                           f"RMSE={metrics['rmse_by_horizon'][h]:.6f}, "
                           f"R²={metrics['r2_by_horizon'][h]:.6f}, "
                           f"Pearson R={metrics['pearson_by_horizon'][h]:.6f}")
    
    # 计算改进百分比
    if 'TEC-MoLLM' in results and 'HistoricalAverage' in results:
        logging.info("\n🎯 TEC-MoLLM相对于历史平均的改进:")
        tec_metrics = results['TEC-MoLLM']
        ha_metrics = results['HistoricalAverage']
        
        mae_improvement = ((ha_metrics['mae_avg'] - tec_metrics['mae_avg']) / ha_metrics['mae_avg']) * 100
        rmse_improvement = ((ha_metrics['rmse_avg'] - tec_metrics['rmse_avg']) / ha_metrics['rmse_avg']) * 100
        r2_improvement = ((tec_metrics['r2_score_avg'] - ha_metrics['r2_score_avg']) / abs(ha_metrics['r2_score_avg'])) * 100
        pearson_improvement = ((tec_metrics['pearson_r_avg'] - ha_metrics['pearson_r_avg']) / ha_metrics['pearson_r_avg']) * 100
        
        logging.info(f"  MAE改进:     {mae_improvement:+.2f}%")
        logging.info(f"  RMSE改进:    {rmse_improvement:+.2f}%") 
        logging.info(f"  R²改进:      {r2_improvement:+.2f}%")
        logging.info(f"  Pearson R改进: {pearson_improvement:+.2f}%")
    
    logging.info("="*80)
    
    # 保存结果
    output_path = os.path.join(args.output_dir, "evaluation_results.csv")
    results_df.to_csv(output_path)
    logging.info(f"详细结果已保存到: {output_path}")
    
    # 保存简要结果摘要
    summary_path = os.path.join(args.output_dir, "evaluation_summary.txt")
    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write("TEC-MoLLM模型评估结果摘要\n")
        f.write("="*50 + "\n\n")
        
        for model_name, metrics in results.items():
            f.write(f"{model_name}:\n")
            f.write(f"  平均MAE:  {metrics['mae_avg']:.6f}\n")
            f.write(f"  平均RMSE: {metrics['rmse_avg']:.6f}\n")
            f.write(f"  平均R²:   {metrics['r2_score_avg']:.6f}\n")
            f.write(f"  平均Pearson R: {metrics['pearson_r_avg']:.6f}\n\n")
    
    logging.info(f"结果摘要已保存到: {summary_path}")

if __name__ == '__main__':
    main() 


================================================
FILE: train.py
================================================
import torch
import torch._dynamo
import torch.nn as nn
from torch.utils.data import DataLoader
from torch.utils.data.distributed import DistributedSampler
import torch.distributed as dist
import torch.multiprocessing as mp
import numpy as np
import logging
import os
import joblib
import argparse
from tqdm import tqdm

from src.data.dataset import SlidingWindowSamplerDataset
# The following imports are now handled by the offline preprocess.py script
# from src.features.feature_engineering import create_features_and_targets, standardize_features
from src.model.tec_mollm import TEC_MoLLM
from src.evaluation.metrics import evaluate_horizons

# Suppress torch.compile backend errors and fallback to eager execution
torch._dynamo.config.suppress_errors = True

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def setup_distributed():
    """Initialize distributed training"""
    if 'RANK' in os.environ and 'WORLD_SIZE' in os.environ:
        rank = int(os.environ['RANK'])
        world_size = int(os.environ['WORLD_SIZE'])
        local_rank = int(os.environ['LOCAL_RANK'])
        
        dist.init_process_group(backend='nccl', rank=rank, world_size=world_size)
        torch.cuda.set_device(local_rank)
        
        return rank, world_size, local_rank
    else:
        return 0, 1, 0

def cleanup_distributed():
    """Clean up distributed training"""
    if dist.is_initialized():
        dist.destroy_process_group()

def train_one_epoch(model, dataloader, optimizer, loss_fn, device, edge_index, scaler, rank=0, accumulation_steps=1):
    model.train()
    total_loss = 0
    progress_bar = tqdm(dataloader, desc="Training") if rank == 0 else dataloader
    
    optimizer.zero_grad()
    
    for i, batch in enumerate(progress_bar):
        x = batch['x'].to(device)
        y = batch['y'].to(device)
        time_features = batch['x_time_features'].to(device)
        
        B, L, H, W, C = x.shape
        x = x.view(B, L, H * W, C)
        if time_features.numel() > 0:
            time_features = time_features.unsqueeze(-2).expand(B, L, H * W, -1)

        # Use autocast for mixed-precision training
        with torch.autocast(device_type='cuda', dtype=torch.float16):
            output = model(x, time_features, edge_index)
            y_reshaped = y.permute(0, 3, 1, 2).reshape(B, -1, H * W, 1)
            loss = loss_fn(output, y_reshaped)
            loss = loss / accumulation_steps
        
        # Scale loss and perform backward pass
        scaler.scale(loss).backward()
        
        if (i + 1) % accumulation_steps == 0:
            scaler.step(optimizer)
            scaler.update()
            optimizer.zero_grad()
        
        total_loss += loss.item() * accumulation_steps
    
    if (len(dataloader)) % accumulation_steps != 0:
        scaler.step(optimizer)
        scaler.update()
        optimizer.zero_grad()
    
    return total_loss / len(dataloader)

def validate(model, dataloader, loss_fn, device, edge_index, target_scaler_path, rank=0):
    model.eval()
    total_loss = 0
    all_preds = []
    all_trues = []
    with torch.no_grad():
        progress_bar = tqdm(dataloader, desc="Validating") if rank == 0 else dataloader
        for batch in progress_bar:
            x = batch['x'].to(device)
            y = batch['y'].to(device)
            time_features = batch['x_time_features'].to(device)

            B, L, H, W, C = x.shape
            x = x.view(B, L, H * W, C)
            if time_features.numel() > 0:
                time_features = time_features.unsqueeze(-2).expand(B, L, H * W, -1)
            
            # Use autocast in validation for consistency
            with torch.autocast(device_type='cuda', dtype=torch.float16):
                output = model(x, time_features, edge_index)
                y_reshaped = y.permute(0, 3, 1, 2).reshape(B, -1, H * W, 1)
                loss = loss_fn(output, y_reshaped)
            
            total_loss += loss.item()
            
            all_preds.append(output.cpu().numpy())
            all_trues.append(y_reshaped.cpu().numpy())
            
    avg_loss = total_loss / len(dataloader)
    
    # Concatenate all batches
    y_true_scaled = np.concatenate(all_trues, axis=0)
    y_pred_scaled = np.concatenate(all_preds, axis=0)
    
    # 调用新的评估函数，传入scaler路径进行逆变换
    metrics = evaluate_horizons(y_true_scaled, y_pred_scaled, target_scaler_path)
    return avg_loss, metrics

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train TEC-MoLLM model')
    
    # Data configuration
    parser.add_argument('--L_in', type=int, default=48, help='Input sequence length (default: 48, adjusted to be feasible)')
    parser.add_argument('--L_out', type=int, default=12, help='Output sequence length (prediction horizon)')
    parser.add_argument('--use_subset', action='store_true', help='Use small data subset for quick testing (ignored with preprocessed data)')
    parser.add_argument('--subset_size', type=int, default=500, help='Size of data subset for training')
    
    # Training configuration
    parser.add_argument('--epochs', type=int, default=50, help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=16, help='Batch size (default: 16, to be used with gradient accumulation)')
    parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--patience', type=int, default=20, help='Early stopping patience (epochs without improvement)')
    parser.add_argument('--min_delta', type=float, default=1e-4, help='Minimum change to qualify as improvement')
    parser.add_argument('--accumulation_steps', type=int, default=1, help='Gradient accumulation steps for handling large sequences')
    
    # Model configuration
    parser.add_argument('--d_emb', type=int, default=16, help='Embedding dimension')
    parser.add_argument('--llm_layers', type=int, default=3, help='Number of LLM layers')
    
    return parser.parse_args()

def main():
    args = parse_args()
    
    # --- Distributed Setup ---
    rank, world_size, local_rank = setup_distributed()
    device = torch.device(f"cuda:{local_rank}" if torch.cuda.is_available() else "cpu")
    
    # Only log from rank 0 to avoid duplicate messages
    if rank == 0:
        logging.info(f"Using device: {device}")
        logging.info(f"Distributed training: rank {rank}/{world_size}")
        logging.info(f"Training configuration: L_in={args.L_in}, L_out={args.L_out}, epochs={args.epochs}")
    
    # --- Define paths ---
    data_dir = 'data/processed'
    graph_path = os.path.join(data_dir, 'graph_A.pt')
    target_scaler_path = os.path.join(data_dir, 'target_scaler.joblib')
    checkpoint_dir = 'checkpoints'
    os.makedirs(checkpoint_dir, exist_ok=True)

    # Calculate temporal sequence length after convolutions based on L_in
    # L_in -> L_in//2 -> L_in//4 (with strides [2, 2])
    conv_output_len = args.L_in // (2 * 2)  # After two stride-2 convolutions
    patch_len = 4  # Use patch_len=4 for proper LLM input
    num_patches = conv_output_len // patch_len
    
    # Ensure patch_len divides evenly into conv_output_len
    if conv_output_len % patch_len != 0:
        # Adjust patch_len to fit
        patch_len = 2 if conv_output_len % 2 == 0 else 1
        num_patches = conv_output_len // patch_len
        logging.warning(f"Adjusted patch_len to {patch_len} to fit conv_output_len {conv_output_len}")
    
    model_config = {
        "num_nodes": 2911, "d_emb": args.d_emb, "spatial_in_channels_base": 6,
        "spatial_out_channels": 32, "spatial_heads": 2, "temporal_channel_list": [64, 128],
        "temporal_strides": [2, 2], "patch_len": patch_len, "d_llm": 768, "llm_layers": args.llm_layers,
        "prediction_horizon": args.L_out, "temporal_seq_len": args.L_in
    }
    
    # --- Data Loading and Processing (Simplified) ---
    # The complex data loading and feature engineering is now done offline.
    # We just need to instantiate the new Dataset class.
    
    if rank == 0:
        logging.info("Loading pre-processed data...")

    # 验证target_scaler文件存在（用于后续评估）
    if not os.path.exists(target_scaler_path):
        logging.error(f"Target scaler not found at {target_scaler_path}. Cannot proceed.")
        cleanup_distributed()
        return
    elif rank == 0:
        logging.info(f"Target scaler path verified: {target_scaler_path}")

    train_dataset = SlidingWindowSamplerDataset(data_path=data_dir, mode='train', L_in=args.L_in, L_out=args.L_out)
    val_dataset = SlidingWindowSamplerDataset(data_path=data_dir, mode='val', L_in=args.L_in, L_out=args.L_out)

    edge_index = torch.load(graph_path)['edge_index'].to(device)

    # Subset logic is removed as it's better handled during offline preprocessing if needed.
    # If required, a separate set of smaller .pt files should be created.
    if args.use_subset:
        if rank == 0:
            logging.warning("Warning: --use_subset is ignored with the new pre-processed data pipeline.")
            logging.warning("For testing, create smaller .pt files with the preprocessing script.")

    if rank == 0:
        logging.info(f"Training dataset size: {len(train_dataset)} samples")
        logging.info(f"Validation dataset size: {len(val_dataset)} samples")
    
    # Create distributed samplers
    train_sampler = DistributedSampler(train_dataset, num_replicas=world_size, rank=rank, shuffle=True) if world_size > 1 else None
    val_sampler = DistributedSampler(val_dataset, num_replicas=world_size, rank=rank, shuffle=False) if world_size > 1 else None
    
    # --- DataLoader Optimization ---
    # As per PRD, optimize DataLoader parameters for H20 GPU.
    train_loader = DataLoader(
        train_dataset, 
        batch_size=args.batch_size, 
        shuffle=(train_sampler is None), 
        sampler=train_sampler,
        num_workers=4,          # PRD: Set to 16, reduced to 4 to avoid shared memory issues
        pin_memory=True,         # PRD: Set to True
        prefetch_factor=4        # PRD: Set to 4
    )
    val_loader = DataLoader(
        val_dataset, 
        batch_size=args.batch_size, 
        shuffle=False, 
        sampler=val_sampler,
        num_workers=4,          # PRD: Set to 16, reduced to 4 to avoid shared memory issues
        pin_memory=True,         # PRD: Set to True
        prefetch_factor=4        # PRD: Set to 4
    )
    
    # --- Model, Optimizer, Loss ---
    model = TEC_MoLLM(model_config).to(device)
    
    # --- Performance Optimizations ---
    # 1. Compile the model for maximum performance (must be done after moving to device)
    if rank == 0:
        logging.info("Compiling the model with torch.compile()...")
    model = torch.compile(model)
    if rank == 0:
        logging.info("Model compiled successfully.")

    # 2. Initialize Gradient Scaler for mixed-precision training
    scaler = torch.cuda.amp.GradScaler()

    # Wrap model for distributed training
    if world_size > 1:
        model = nn.parallel.DistributedDataParallel(model, device_ids=[local_rank])
    
    optimizer = torch.optim.AdamW(filter(lambda p: p.requires_grad, model.parameters()), lr=args.lr)
    loss_fn = nn.MSELoss()
    
    # --- Training Loop ---
    best_val_loss = float('inf')
    patience_counter = 0
    
    try:
        for epoch in range(args.epochs):
            # Set epoch for distributed sampler
            if train_sampler is not None:
                train_sampler.set_epoch(epoch)
            
            if rank == 0:
                logging.info(f"--- Epoch {epoch+1}/{args.epochs} ---")
            
            train_loss = train_one_epoch(model, train_loader, optimizer, loss_fn, device, edge_index, scaler, rank, args.accumulation_steps)
            val_loss, val_metrics = validate(model, val_loader, loss_fn, device, edge_index, target_scaler_path, rank)
            
            if rank == 0:
                # 每个epoch都显示基本信息
                logging.info(f"Train Loss: {train_loss:.4f} | Val Loss: {val_loss:.4f}")
                
                # 每10个epoch或最后一个epoch显示详细指标
                if (epoch + 1) % 10 == 0 or epoch == args.epochs - 1:
                    logging.info("="*80)
                    logging.info(f"DETAILED METRICS - Epoch {epoch+1}/{args.epochs}")
                    logging.info("="*80)
                    logging.info(f"Training Loss: {train_loss:.6f}")
                    logging.info(f"Validation Loss: {val_loss:.6f}")
                    logging.info("Validation Metrics by Horizon:")
                    logging.info(f"  - MAE Average: {val_metrics['mae_avg']:.6f}")
                    logging.info(f"  - RMSE Average: {val_metrics['rmse_avg']:.6f}")
                    logging.info(f"  - R² Score Average: {val_metrics['r2_score_avg']:.6f}")
                    logging.info(f"  - Pearson R Average: {val_metrics['pearson_r_avg']:.6f}")
                    
                    # 如果val_metrics包含各个horizon的详细指标，也打印出来
                    if 'mae_by_horizon' in val_metrics:
                        logging.info("MAE by Horizon:")
                        for h, mae in enumerate(val_metrics['mae_by_horizon'], 1):
                            logging.info(f"  Horizon {h:2d}: {mae:.6f}")
                    
                    if 'rmse_by_horizon' in val_metrics:
                        logging.info("RMSE by Horizon:")
                        for h, rmse in enumerate(val_metrics['rmse_by_horizon'], 1):
                            logging.info(f"  Horizon {h:2d}: {rmse:.6f}")
                            
                    if 'r2_by_horizon' in val_metrics:
                        logging.info("R² Score by Horizon:")
                        for h, r2 in enumerate(val_metrics['r2_by_horizon'], 1):
                            logging.info(f"  Horizon {h:2d}: {r2:.6f}")
                            
                    if 'pearson_by_horizon' in val_metrics:
                        logging.info("Pearson R by Horizon:")
                        for h, pearson in enumerate(val_metrics['pearson_by_horizon'], 1):
                            logging.info(f"  Horizon {h:2d}: {pearson:.6f}")
                    
                    logging.info(f"Best Validation Loss So Far: {best_val_loss:.6f}")
                    logging.info("="*80)
                else:
                    # 简单显示关键指标
                    logging.info(f"Val R²: {val_metrics['r2_score_avg']:.4f} | Pearson R: {val_metrics['pearson_r_avg']:.4f}")
                
                # Early stopping and model saving logic
                if val_loss < best_val_loss - args.min_delta:
                    best_val_loss = val_loss
                    patience_counter = 0
                    checkpoint_path = os.path.join(checkpoint_dir, 'best_model.pth')
                    # Save the unwrapped model state for DDP
                    model_to_save = model.module if hasattr(model, 'module') else model
                    torch.save(model_to_save.state_dict(), checkpoint_path)
                    logging.info(f"🎉 New best model saved to {checkpoint_path} (Val Loss: {val_loss:.6f})")
                else:
                    patience_counter += 1
                    logging.info(f"No improvement for {patience_counter}/{args.patience} epochs")
                
                # Early stopping check
                if patience_counter >= args.patience:
                    logging.info(f"🛑 Early stopping triggered after {epoch+1} epochs (patience: {args.patience})")
                    logging.info(f"Best validation loss: {best_val_loss:.6f}")
                    break
    
    finally:
        cleanup_distributed()

if __name__ == '__main__':
    main() 


================================================
FILE: experiments/l_in_study/experiment_results.csv
================================================
L_in,batch_size,accumulation_steps,effective_batch_size,mae_avg,rmse_avg,r2_score_avg,pearson_r_avg,training_time_hours,status
48,4,1,4,114.757,200.126,0.7736,0.8808,1.5,completed


================================================
FILE: experiments/l_in_study/revised_experiment_plan.md
================================================
# 修订版 L_in 调优实验计划

## 实验结果总结

### 已完成实验
- **L_in=48**: ✅ 成功完成
  - MAE: 114.757, RMSE: 200.126, R²: 0.7736, Pearson R: 0.8809
  - batch_size=4, 训练时间1.5小时
  - 性能优秀，作为基准

### 遇到的问题
- **L_in=96**: ❌ 显存不足
  - batch_size=4时训练OOM
  - batch_size=2时训练可行但推理OOM
  - 无法完成完整的性能评估

## 修订策略

基于硬件限制，采用渐进式实验策略：

### 实验组设计
1. **L_in=48** (已完成): 基准性能
2. **L_in=72**: 中等步长，测试性能提升潜力
3. **L_in=84**: 接近极限的测试
4. **分析与总结**: 基于可完成的实验绘制性能曲线

### 技术参数调整
- L_in=72: batch_size=2, accumulation_steps=2 (等效batch_size=4)
- L_in=84: batch_size=1, accumulation_steps=4 (等效batch_size=4) 
- 如果仍有显存问题，降低batch_size并相应调整accumulation_steps

### 预期成果
- 明确在当前硬件条件下的L_in上限
- 量化历史序列长度对预测性能的影响
- 为实际部署提供性能-资源平衡的参考

## 关键发现
更长的历史输入序列虽然理论上能提供更多信息，但在实际应用中需要考虑：
1. 显存占用呈平方级增长
2. 推理时间显著增加
3. 收益递减效应

这正验证了您提到的"性能与资源最佳平衡点"的重要性。


================================================
FILE: plans/corect_revised.md
================================================
**核心诊断：模型遇到了一个瓶颈，无法进一步降低验证损失，这很可能是一个或多个系统性问题共同作用的结果，而不仅仅是训练时长不足。**

我们来逐一分析。

### 1. 结果解读：模型“学会了”，但“学得不好”

*   **R²和Pearson R表现尚可**: 平均`R²=0.67`和`Pearson R=0.82`表明，模型确实捕捉到了数据的主要变化趋势和大部分方差。它知道什么时候TEC该高，什么时候该低。这证明**模型的基本架构和数据流是通的**。
*   **MAE/RMSE巨大，且损失不再下降**: `Val Loss: 259.51`相比第一轮的`246.17`甚至还**升高了**。这说明模型在第1个epoch之后就**陷入了一个性能瓶颈或局部最优解**，后续的19个epoch并没有带来任何实质性的改进。巨大的绝对误差说明模型的预测值在**数值尺度**上与真实值有系统性的偏差。

### 2. 问题根源的深度排查

既然增加训练时长无效，我们就必须从更根本的地方寻找问题。根据我的经验，以下几个方面是最大“嫌疑犯”，请您逐一排查。

#### **嫌疑犯一：数据标准化和逆标准化流程中的尺度不匹配 (最可能的原因)**

这是导致MAE/RMSE巨大而R²/Pearson尚可的**最典型原因**。

*   **问题描述**: 模型在训练时看到的是**标准化后**的`Y`（或说损失是在标准化尺度上计算的），但在评估时，`evaluate_horizons`函数需要对预测值和真实值进行**逆标准化**来计算真实误差。如果这两个过程使用的`scaler`不匹配，就会导致巨大的数值偏差。
*   **排查点**:
    
    1.  **`train.py`中的损失计算**:
        ```python
        # train.py
        loss = loss_fn(output, y_reshaped) 
        ```
        这里的`output`和`y_reshaped`都是**标准化尺度**的吗？`y_reshaped`来自`SlidingWindowSamplerDataset`，它拿的是标准化的`Y`。`output`是模型预测值。如果`Y`在送入`Dataset`前没有被标准化，那么模型学的目标就是原始尺度，而输入是标准化尺度，这会造成混乱。**请确认`standardize_features`是否也对`Y`进行了标准化。** 从您之前的代码看，它只标准化了`X`。这是一个潜在的巨大问题。
    2.  **`evaluate_horizons`中的逆标准化**:
        
        ```python
        # evaluation/metrics.py
        y_true_unscaled = scaler.inverse_transform(y_true)
        y_pred_unscaled = scaler.inverse_transform(y_pred)
        ```
        这里的`scaler`是`target_scaler.joblib`，它是只用TEC的第一个特征列拟合的。而`y_pred`是模型输出，`y_true`是真实的`Y`。如果它们的维度和scaler期望的维度不匹配，逆变换就会出错。
*   **【必须采取的行动】**:
    1.  **统一尺度**: 确保模型训练和评估的尺度一致。最佳实践是：
        *   **只对输入`X`做标准化**。
        *   让模型直接学习预测**原始尺度**的`Y`。这意味着损失函数是在原始尺度上计算的。
        *   或者，将`X`和`Y`**都用同一个`scaler`标准化**，并在评估时用同一个`scaler`逆变换。
    2.  **检查`target_scaler`**: 在`train.py`中，您为目标单独创建了`target_scaler`。请确认在`validate`函数中，传递给`evaluate_horizons`的是这个`target_scaler_path`，并且`y_pred`和`y_true`在送入`evaluate_metrics`前都已经被正确地`reshape`为`(-1, 1)`以匹配这个scaler。

#### **嫌疑犯二：模型输出层的激活函数缺失**

*   **问题描述**: 模型的`PredictionHead`是一个纯线性层。线性层的输出可以是任意实数（负数、很大的正数）。但TEC值是**非负**的，并且通常有一个大致的范围。
*   **排查点**:
    *   `src/model/modules.py` -> `PredictionHead`。
*   **【建议的修改】**:
    *   在`PredictionHead`的`fc`层后增加一个**激活函数**来约束输出范围。
        ```python
        # in modules.py/PredictionHead
        class PredictionHead(nn.Module):
            def __init__(...):
                self.fc = nn.Linear(...)
                self.activation = nn.ReLU() # 或者 nn.Softplus()
            
            def forward(self, x):
                # ...
                output = self.fc(x)
                output = self.activation(output) # 保证输出非负
                return output
        ```
    *   `ReLU`可以直接保证输出非负。`Softplus`是一个更平滑的选择。这个简单的修改可以防止模型预测出无意义的负值，并帮助稳定训练。

#### **嫌疑犯三：模型架构与数据流中的维度问题**

虽然我们之前检查过，但复杂的`reshape`和`permute`操作是bug的温床。

*   **问题描述**: 模型中某个环节的维度变换可能不符合预期，导致信息被错误地混合或丢失。
*   **排查点**:
    *   **`tec_mollm.py`的`forward`函数**是核心排查区域。
*   **【建议的调试方法】**:
    *   在`forward`函数的**每一步**后，都加上`print(f"Step X, shape: {x.shape}")`。
    *   用一个`batch_size=1`的输入跑一遍模型，手动地、一步步地推导维度的变化，确保它与您的预期完全一致。特别是从`SpatialEncoder`输出到`TemporalEncoder`输入，以及从`LLMBackbone`输出到`PredictionHead`输入这几个关键的`reshape`环节。

#### **嫌疑犯四：学习率依然不合适**

*   **问题描述**: 即使提高了学习率，如果依然太大或太小，模型都可能无法收敛。
*   **【建议的实验】**:
    *   进行一次**学习率范围测试 (LR Range Test)**。让学习率从一个很小的值（如`1e-7`）线性或指数增加到一个很大的值（如`1e-1`），并记录下每个step的损失。
    *   绘制**学习率 vs. 损失**的曲线。损失开始急剧下降然后又开始发散的那个学习率区间，就是最佳学习率所在的范围。这是寻找最佳学习率的黄金法则。

### 总结与优先级行动计划

**当前模型表现不佳，最可能的原因是系统性的数据尺度问题，其次是模型输出约束和学习率。**

**请按以下顺序排查和修改：**

1.  **【最高优先级】检查并统一数据尺度**:
    *   **确认 `standardize_features` 是否只处理了 `X`。**
    *   **决策**:
        *   **方案A (推荐)**: 修改训练逻辑，让模型直接预测原始尺度的`Y`。这意味着，`y_reshaped`不需要标准化，并且`loss_fn`在原始尺度上计算。`validate`函数中，`y_pred`也无需逆变换，只需将`y_true`逆变换即可比较。
        *   **方案B**: 用同一个`scaler`（在`X`的所有6个特征上拟合的）同时标准化`X`和`Y`的TEC通道。
    *   **确保评估时`inverse_transform`的输入维度与`scaler`拟合时的维度一致。**

2.  **【高优先级】为`PredictionHead`添加非线性激活函数**:
    *   加入`nn.ReLU()`或`nn.Softplus()`，确保预测值为非负。

3.  **【中等优先级】进行学习率范围测试**:
    *   找到一个更优的学习率区间，而不是靠猜测。

完成以上步骤后，再重新运行一个20-30 epochs的训练。我非常有信心，在解决了这些根本性的问题之后，您的模型性能将会迎来一次**真正的、数量级上的飞跃**，MAE和RMSE会大幅下降，R²会非常接近1。


================================================
FILE: plans/h20_optimization.prd
================================================
### PRD: H20 GPU 性能极致优化

#### 1. 项目概述与核心目标

本项目旨在针对单张NVIDIA H20 (96GB) GPU的顶级硬件环境，对现有的时间序列预测模型训练流程进行端到端的性能优化。核心瓶颈已从GPU算力/显存转移至数据I/O和CPU预处理。

**最终目标：** 通过重构数据流水线和优化训练脚本，将H20 GPU的利用率（GPU Util）稳定在90%以上，最大化硬件潜力，显著缩短模型训练和实验周期。

---

#### 2. 功能需求与任务分解

##### **里程碑 1: 数据预处理流水线重构**

**任务 1.1: 创建离线数据预处理脚本 (`preprocess.py`)**
*   **需求**: 编写一个一次性执行的Python脚本 `preprocess.py`。
*   **功能细节**:
    1.  从 `data/raw/` 目录加载原始的HDF5数据文件。
    2.  执行所有必要的特征工程和标准化操作。
    3.  将处理完成、维度对齐的训练集、验证集、测试集数据（包含 `X`, `Y`, `time_features`）分别保存为3个独立的大型PyTorch张量文件。
    4.  **产物**:
        *   `data/processed/train_set.pt`
        *   `data/processed/validation_set.pt`
        *   `data/processed/test_set.pt`
*   **验收标准**: 脚本成功运行，并在指定目录下生成三个`.pt`文件。

**任务 1.2: 重构PyTorch `Dataset` 类**
*   **需求**: 修改现有的`Dataset`实现，以适配离线处理好的数据。
*   **功能细节**:
    1.  在`__init__`方法中，根据传入的模式（'train', 'val', 'test'），直接使用`torch.load()`将对应的`*.pt`文件（例如 `data/processed/train_set.pt`）一次性完整加载到CPU内存中。
    2.  重写`__getitem__`方法，使其逻辑极其轻量。它唯一的任务就是根据索引`idx`，从已加载在内存中的巨大张量上执行切片操作，返回一个`(X_sample, Y_sample, time_features_sample)`元组。
*   **验收标准**: `Dataset`对象可以被成功实例化，并且`__getitem__`的CPU执行时间显著降低。

##### **里程碑 2: 训练脚本极致优化**

**任务 2.1: 优化`DataLoader`配置**
*   **需求**: 在`train.py`中，调整`DataLoader`的实例化参数以最大化数据吞吐量。
*   **功能细节**:
    *   `num_workers`: 设为 `16`。
    *   `pin_memory`: 设为 `True`。
    *   `prefetch_factor`: 设为 `4`。
*   **验收标准**: `DataLoader`在训练循环中能够持续、高效地向GPU提供数据。

**任务 2.2: 启用混合精度训练与`torch.compile`**
*   **需求**: 在`train.py`的训练循环中，全面启用针对Hopper架构的性能优化特性。
*   **功能细节**:
    1.  引入`torch.cuda.amp.GradScaler`和`autocast`上下文管理器。
    2.  在模型前向传播和损失计算部分，使用`with autocast(dtype=torch.bfloat16):`进行包裹。
    3.  使用`GradScaler`实例对损失进行缩放（`scale`）、反向传播、更新优化器（`step`）和更新缩放器（`update`）。
    4.  在模型移动到device (`.to(device)`) 之后，立即使用`model = torch.compile(model)`对模型进行编译。
*   **验收标准**: 训练能够以`bfloat16`混合精度正确运行，且`torch.compile`无报错。

**任务 2.3: 调整核心超参数以利用大显存**
*   **需求**: 修改`train.py`的默认超参数，以充分利用H20的96GB显存。
*   **功能细节**:
    *   `--L_in` (输入序列长度): 默认值设为 `336`。
    *   `--batch_size` (批次大小): 默认值设为 `64`。
*   **验收标准**: 训练能够以指定的更大输入长度和批次大小启动并稳定运行。

##### **里程碑 3: 验证与性能监控**

**任务 3.1: 执行优化后的训练并进行性能验证**
*   **需求**: 运行完整的训练流程，并监控关键性能指标以确认优化效果。
*   **功能细节**:
    1.  使用优化后的`train.py`脚本启动训练。
    2.  在训练过程中，使用`nvidia-smi dmon`或`gpustat -i`监控GPU。
    3.  使用`htop`或类似工具监控CPU核心的负载情况。
*   **验收标准**:
    *   **首要标准**: GPU利用率（GPU-Util）在训练的大部分时间内稳定在**90%以上**。
    *   显存利用率（Memory-Util）在设定的`batch_size`下处于一个较高但安全（无OOM）的水平。
    *   CPU的16个核心表现出持续的负载，证明`num_workers`在有效工作。
    *   记录下最终达成的稳定训练速度（e.g., iterations/sec）。 


================================================
FILE: plans/revised.md
================================================
好的，根据您这套顶级的硬件配置——**单张NVIDIA H20 (96GB)**，我们可以制定一套**最大化利用硬件潜力**的优化方案。之前的瓶颈是显存和算力，现在瓶颈完全转移到了**数据I/O和CPU预处理**上。

我们的核心目标是：**用16核CPU疯狂地准备数据，把数据流“喂”满H20这头“性能猛兽”，让它的GPU利用率尽可能接近100%。**

---

### 优化计划：充分利用H20的优势

#### 1. 软件环境与编译优化

*   **PyTorch版本**: 确保您的PyTorch版本是**2.1或更高**，并且是**为Hopper架构（CUDA 12.x）编译的**。这能确保您能使用H20的**Transformer Engine (FP8支持)**。
*   **CUDA & cuDNN**: 确保安装了与PyTorch版本配套的最新CUDA Toolkit和cuDNN。

#### 2. 数据加载与预处理 (`DataLoader`极致优化)

这是最重要的优化环节。

*   **`num_workers`设置**:
    *   **操作**: 在`DataLoader`中，将`num_workers`设置为一个较高的值。
    *   **建议**: 您的CPU有16个vCPU，可以从`num_workers=16`开始尝试，甚至可以设为`24`或`32`（通常I/O密集型任务，worker数可以超过CPU核心数）。您需要实验找到最佳值。
        ```python
        # in train.py
        train_loader = DataLoader(..., num_workers=16, pin_memory=True, prefetch_factor=4)
        ```
    *   **`prefetch_factor`**: 当`num_workers>0`时，可以设置`prefetch_factor=4`（或更高）。这会让每个worker提前加载4个batch的数据，进一步减少GPU的等待时间。

*   **数据离线化 (强烈建议)**:
    *   **问题**: 即使有16个worker，如果每个worker都要在`__getitem__`里执行复杂的滑动窗口切片，依然会消耗大量CPU时间。
    *   **操作**:
        1.  **编写一次性预处理脚本 `preprocess.py`**:
            *   这个脚本只运行一次，**它唯一的任务就是生成训练、验证、测试所需的所有样本**。
            *   它会加载原始HDF5，进行特征工程、标准化，然后执行**滑动窗口采样**。
            *   将每个采样出的`(X_sample, Y_sample, time_features_sample)`保存为**独立的`.pt`文件**。
            *   **目录结构**:
                ```
                data/processed/
                ├── train/
                │   ├── sample_00000.pt
                │   ├── sample_00001.pt
                │   └── ...
                ├── val/
                │   └── ...
                └── test/
                    └── ...
                ```
        2.  **修改`Dataset`类**:
            *   新的`Dataset`类在`__init__`时，只需扫描对应目录下的所有`.pt`文件名，获取样本总数。
            *   `__getitem__`函数变得极其简单：根据索引`idx`，直接`torch.load()`对应的`sample_XXXXX.pt`文件即可。
    *   **优势**:
        *   **I/O瓶颈最小化**: `__getitem__`的CPU计算时间几乎为0。
        *   **磁盘瓶颈**: 您的系统盘只有30GB，可能无法存下所有预处理好的样本。**这是一个新的、需要注意的瓶颈！**
            *   **解决方案**: 如果磁盘空间不足，可以考虑**部分离线化**：预处理脚本不切分样本，而是将整个处理好的、对齐的`X`, `Y`, `time_features`张量保存为**单个大型`.pt`或`.npy`文件**。然后在`Dataset`中用`np.memmap`或直接加载整个张量到内存（150GB内存应该足够），再进行切片。**考虑到您有150GB内存，后者（一次性加载到内存）是最佳选择。**

#### 3. 训练脚本与模型 (`train.py`) 优化

*   **充分利用大显存——增大批次大小和输入长度**:
    *   **操作**: 这是发挥H20 96GB显存优势的关键。
        *   `--L_in`: **直接使用`336`作为起点**，甚至可以尝试`672`。
        *   `--batch_size`: **大胆地增加**。在`L_in=336`下，您可以从`32`开始尝试，逐步增加到`64`, `128`，直到接近显存上限。
    *   **收益**: 大`batch_size`可以提供更稳定的梯度，可能允许您使用更高的学习率，从而**加速收敛**，减少总训练epochs。

*   **启用FP8/BF16混合精度训练与Transformer Engine**:
    *   **操作**: 使用PyTorch的`torch.cuda.amp`（自动混合精度）。
        ```python
        # in train.py
        from torch.cuda.amp import GradScaler, autocast

        scaler = GradScaler()

        # in training loop
        for batch in dataloader:
            optimizer.zero_grad()
            
            with autocast(dtype=torch.bfloat16): # 或者 torch.float16
                output = model(...)
                loss = loss_fn(...)

            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
        ```
    *   **优势**:
        *   **速度翻倍**: 对于Hopper架构，使用`bfloat16`或`float16`能激活Tensor Core，特别是Transformer Engine的FP8优化，可以使训练速度**接近翻倍**。
        *   **显存减半**: 模型参数、激活值、梯度都以16位存储，**显存占用几乎减半**，让您可以进一步增大`batch_size`或模型复杂度。

*   **启用 `torch.compile`**:
    *   **操作**: 在`model.to(device)`之后，加入`model = torch.compile(model)`。
    *   **优势**: 对于复杂的模型，`torch.compile`能进行算子融合等优化，减少GPU kernel的启动开销，进一步提升训练速度。

### 优化后的实施计划

1.  **数据预处理 (一次性任务)**:
    *   编写`preprocess.py`。
    *   加载HDF5，完成所有特征工程和标准化。
    *   将最终的、对齐的`train`, `val`, `test`三大块数据（`X`, `Y`, `time_features`）分别保存为**大型`.pt`文件**，例如 `data/processed/train_data.pt`。

2.  **Dataset类修改**:
    *   `__init__`: 加载整个`.pt`文件到内存中。
    *   `__getitem__`: 直接从内存中的大张量上进行切片，返回样本。

3.  **训练脚本 `train.py` 修改**:
    *   **DataLoader**: 设置`num_workers=16`, `pin_memory=True`, `prefetch_factor=4`。
    *   **混合精度**: 引入`torch.cuda.amp.autocast`和`GradScaler`。
    *   **模型编译**: 加入`torch.compile(model)`。

4.  **运行实验**:
    *   **启动命令**: `python train.py --L_in 336 --batch_size 64 --epochs 100` (这是一个示例，您可以根据显存情况调整`batch_size`)。
    *   **监控**:
        *   运行`gpustat -i` 或 `nvidia-smi dmon`。
        *   **观察GPU Util**: 您的目标是让它尽可能稳定在**90%以上**。
        *   **观察Memory Util**: 查看在您设置的`batch_size`下，96GB显存的使用情况，留出一些余量。
        *   **观察CPU Util**: 运行`htop`，查看16个CPU核心是否都在忙碌地为GPU准备数据。

通过这一套极致的优化，您将能把H20的性能压榨到极限，大大缩短您的研究周期，并有能力进行之前无法想象的超大规模实验。


================================================
FILE: scripts/preprocess.py
================================================
import torch
import numpy as np
import logging
import os
import joblib
from sklearn.preprocessing import StandardScaler

# 为脚本添加项目根目录到sys.path，以便能够导入src中的模块
import sys
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.features.feature_engineering import create_features_and_targets, standardize_features

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def main():
    """
    主函数，执行离线数据预处理的完整流程。
    """
    logging.info("--- 开始执行离线数据预处理脚本 ---")

    # 定义输入和输出路径
    # 根据PRD和现有代码，我们处理2014和2015年的数据
    file_paths = [
        'data/raw/CRIM_SW2hr_AI_v1.2_2014_DataDrivenRange_CN.hdf5',
        'data/raw/CRIM_SW2hr_AI_v1.2_2015_DataDrivenRange_CN.hdf5'
    ]
    output_dir = 'data/processed'
    os.makedirs(output_dir, exist_ok=True)
    logging.info(f"输出目录 '{output_dir}' 已确认存在。")

    # --- 1. 创建特征和目标 ---
    # 这个函数内部已经包含了数据加载、拆分和特征工程
    # PRD中默认的L_out是12，这里保持一致
    processed_splits = create_features_and_targets(file_paths, horizon=12)
    if not processed_splits:
        logging.error("创建特征和目标失败。正在中止脚本。")
        return

    # --- 2. 标准化特征 (X) ---
    # 这个函数会拟合并保存scaler.joblib，然后返回标准化后的数据
    scaler_path = os.path.join(output_dir, 'scaler.joblib')
    standardized_splits, _ = standardize_features(processed_splits, scaler_path=scaler_path)

    # --- 3. 为目标(Y)创建并保存独立的scaler ---
    # 这个逻辑来自原始的train.py，为了保持一致性，我们也在这里完成
    target_scaler_path = os.path.join(output_dir, 'target_scaler.joblib')
    target_scaler = StandardScaler()
    # 仅使用训练集中的TEC数据来拟合目标scaler
    # train.py的逻辑是使用processed_splits，即未标准化的X来拟合target_scaler
    train_tec_data = processed_splits['train']['X'][:, :, :, 0:1]  # 仅TEC特征
    train_tec_reshaped = train_tec_data.reshape(-1, 1)
    target_scaler.fit(train_tec_reshaped)
    joblib.dump(target_scaler, target_scaler_path)
    logging.info(f"目标scaler已在训练集上拟合并保存至: {target_scaler_path}")

    # --- 4. 保存处理好的数据为 .pt 文件 ---
    for split_name in ['train', 'val', 'test']:
        logging.info(f"--- 正在处理和保存 '{split_name}' 数据集 ---")
        
        # 从standardized_splits获取标准化的X
        # 从processed_splits获取原始的Y和time_features
        # 注意：standardized_splits只包含X和Y，所以time_features从原始的processed_splits中获取
        standardized_x = standardized_splits[split_name]['X']
        original_y = processed_splits[split_name]['Y']
        time_features = processed_splits[split_name]['time_features']
        
        # --- 新增：对Y进行标准化 ---
        logging.info(f"对 '{split_name}' 的Y进行标准化...")
        # Y的形状是 (N, H, W, L_out)，scaler期望 (n_samples, n_features)
        # target_scaler是为单特征TEC拟合的，所以reshape为 (N*H*W*L_out, 1)
        y_reshaped = original_y.reshape(-1, 1) 
        y_scaled_reshaped = target_scaler.transform(y_reshaped)
        y_scaled = y_scaled_reshaped.reshape(original_y.shape)
        logging.info(f"'{split_name}' 的Y标准化完成。")
        # --- 新增结束 ---
        
        # 将numpy数组转换为torch张量
        x_tensor = torch.from_numpy(standardized_x).float()
        y_tensor = torch.from_numpy(y_scaled).float()  # <--- Y现在是标准化尺度
        tf_tensor = torch.from_numpy(time_features).float()

        logging.info(f"'{split_name}' 张量形状: X={x_tensor.shape}, Y={y_tensor.shape}, time_features={tf_tensor.shape}")

        # 组合成一个字典
        data_to_save = {
            'X': x_tensor,
            'Y': y_tensor,
            'time_features': tf_tensor
        }
        
        # 定义输出文件路径
        output_filepath = os.path.join(output_dir, f"{split_name}_set.pt")
        
        # 保存到文件
        torch.save(data_to_save, output_filepath)
        logging.info(f"'{split_name}' 数据集已成功保存至: {output_filepath}")

    logging.info("--- 离线数据预处理脚本执行完毕 ---")

if __name__ == '__main__':
    main() 


================================================
FILE: src/data/data_loader.py
================================================
import h5py
import numpy as np
import logging
import pandas as pd
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def _load_data_from_hdf5(file_path: str) -> dict:
    """
    (Internal) Reads specified datasets from a single HDF5 file.

    Args:
        file_path (str): The path to the HDF5 file.

    Returns:
        dict: A dictionary containing the datasets. Returns an empty dictionary if an error occurs.
    """
    logging.info(f"Attempting to load data from {file_path}")
    data = {}
    try:
        with h5py.File(file_path, 'r') as f:
            if 'ionosphere/TEC' in f:
                data['tec'] = f['ionosphere/TEC'][:]
                logging.info(f"Loaded 'tec' with shape: {data['tec'].shape}")
            else:
                logging.error("'ionosphere/TEC' not found in the file.")
                return {}

            # Correct path for time information based on h5ls inspection
            if 'coordinates/datetime_utc' in f:
                data['time'] = f['coordinates/datetime_utc'][:]
                logging.info(f"Loaded 'time' with shape: {data['time'].shape}")
            else:
                logging.error("'coordinates/datetime_utc' not found. Time-based split will fail.")
                return {}

            if 'space_weather_indices' in f:
                # This is a group, not a dataset. We need to stack the individual indices.
                try:
                    ae = f['space_weather_indices/AE_Index'][:]
                    dst = f['space_weather_indices/Dst_Index'][:]
                    f107 = f['space_weather_indices/F107_Index'][:]
                    
                    # Handle Kp Index with proper scale_factor
                    kp_raw = f['space_weather_indices/Kp_Index'][:]
                    kp_scale_factor = f['space_weather_indices/Kp_Index'].attrs.get('scale_factor', 1.0)
                    kp = kp_raw * kp_scale_factor
                    logging.info(f"Applied scale_factor {kp_scale_factor} to Kp_Index")
                    
                    ap = f['space_weather_indices/ap_Index'][:]
                    
                    # Stack them into a single array (T, num_indices)
                    data['space_weather_indices'] = np.stack([ae, dst, f107, kp, ap], axis=-1)
                    logging.info(f"Loaded and stacked 'space_weather_indices' with shape: {data['space_weather_indices'].shape}")
                except KeyError as e:
                    logging.error(f"Could not find an index within space_weather_indices group: {e}")
            else:
                logging.warning("'space_weather_indices' group not found.")

            if 'coordinates' in f:
                # We need specific coordinate arrays, not the whole group
                if 'coordinates/latitude' in f and 'coordinates/longitude' in f:
                    data['latitude'] = f['coordinates/latitude'][:]
                    data['longitude'] = f['coordinates/longitude'][:]
                    logging.info(f"Loaded 'latitude' with shape: {data['latitude'].shape}")
                    logging.info(f"Loaded 'longitude' with shape: {data['longitude'].shape}")
                else:
                    logging.warning("Latitude/Longitude not found under /coordinates.")
            else:
                logging.warning("'coordinates' group not found.")

    except FileNotFoundError:
        logging.error(f"File not found: {file_path}")
        return {}
    except Exception as e:
        logging.error(f"An error occurred while reading {file_path}: {e}")
        return {}
    
    logging.info(f"Successfully loaded data from {file_path}")
    return data

def _aggregate_data(file_paths: list) -> dict:
    """
    (Internal) Loads data from a list of HDF5 files and aggregates them.

    Args:
        file_paths (list): A list of paths to the HDF5 files.

    Returns:
        dict: A dictionary containing the aggregated datasets.
    """
    all_data = []
    for path in file_paths:
        data = _load_data_from_hdf5(path)
        if data:
            all_data.append(data)
    
    if not all_data:
        logging.error("No data could be loaded from any file paths.")
        return {}

    # Assuming all files have the same keys and structure
    aggregated = {}
    keys_to_aggregate = ['tec', 'time', 'space_weather_indices']
    
    for key in keys_to_aggregate:
        # Concatenate along the time axis (axis=0)
        aggregated[key] = np.concatenate([d[key] for d in all_data if key in d], axis=0)
        logging.info(f"Aggregated '{key}' with final shape: {aggregated[key].shape}")

    # Convert byte strings to datetime objects for proper comparison and manipulation
    try:
        # The time data is in byte format (e.g., b'2014-01-01T00:00:00'), decode it first
        decoded_time = np.char.decode(aggregated['time'])
        aggregated['time'] = pd.to_datetime(decoded_time)
        logging.info("Successfully converted 'time' array to datetime objects.")
    except Exception as e:
        logging.error(f"Failed to convert time strings to datetime objects: {e}")
        return {}

    # Coordinates are static and should be the same across files, so just take from the first file.
    static_keys = ['latitude', 'longitude']
    for key in static_keys:
        if key in all_data[0]:
            aggregated[key] = all_data[0][key]
            logging.info(f"Took static key '{key}' from first file with shape: {aggregated[key].shape}")

    return aggregated

def _split_data(aggregated_data: dict) -> dict:
    """
    (Internal) Splits the aggregated data into training, validation, and test sets based on date ranges.

    Args:
        aggregated_data (dict): A dictionary containing the aggregated datasets, including a 'time'
                                  key with datetime objects.

    Returns:
        dict: A dictionary containing the data splits, e.g., {'train': {...}, 'val': {...}, 'test': {...}}.
    """
    if 'time' not in aggregated_data:
        logging.error("Cannot split data without 'time' key.")
        return {}
    
    # Create a pandas Series for easy boolean indexing with dates
    timestamps = pd.Series(aggregated_data['time'])

    # Define split boundaries
    train_end = '2014-12-31 23:59:59'
    val_start = '2015-01-01 00:00:00'
    val_end = '2015-06-30 23:59:59'
    test_start = '2015-07-01 00:00:00'

    # Create boolean masks
    train_mask = timestamps <= train_end
    val_mask = (timestamps >= val_start) & (timestamps <= val_end)
    test_mask = timestamps >= test_start

    data_splits = {}
    for split_name, mask in zip(['train', 'val', 'test'], [train_mask, val_mask, test_mask]):
        split = {}
        for key, value in aggregated_data.items():
            if hasattr(value, 'ndim') and value.ndim > 1 or key == 'time': # Split time-series data
                split[key] = value[mask]
            else: # Keep static data like coordinates
                split[key] = value
        data_splits[split_name] = split
        logging.info(f"Created '{split_name}' split with {len(split['time'])} samples.")
        
    return data_splits

def load_and_split_data(file_paths: List[str]) -> Dict[str, Any]:
    """
    Main function to load, aggregate, and split data from HDF5 files.

    This is the primary public function of this module.

    Args:
        file_paths (List[str]): List of paths to the HDF5 files.

    Returns:
        Dict[str, Any]: A dictionary containing the 'train', 'val', and 'test' data splits.
    """
    logging.info("Starting data loading and splitting process...")
    
    # Step 1: Aggregate data from all files
    aggregated_data = _aggregate_data(file_paths)
    if not aggregated_data:
        logging.error("Aggregation step failed. Aborting.")
        return {}
    
    # Step 2: Split the aggregated data
    data_splits = _split_data(aggregated_data)
    if not data_splits:
        logging.error("Splitting step failed. Aborting.")
        return {}
        
    logging.info("Data loading and splitting process completed successfully.")
    return data_splits

if __name__ == '__main__':
    # This block now serves as a final integration test and usage example.
    logging.info("--- Running Master Data Loading Script Test ---")
    
    # Define file paths
    files = [
        'data/raw/CRIM_SW2hr_AI_v1.2_2014_DataDrivenRange_CN.hdf5',
        'data/raw/CRIM_SW2hr_AI_v1.2_2015_DataDrivenRange_CN.hdf5'
    ]
    
    # Run the main function
    final_data = load_and_split_data(files)
    
    # Verification
    if final_data:
        logging.info("Test PASSED: Master script ran successfully.")
        assert 'train' in final_data and 'val' in final_data and 'test' in final_data
        
        # Check sample counts
        assert len(final_data['train']['time']) == 4380
        assert len(final_data['val']['time']) == 2172
        assert len(final_data['test']['time']) == 2208
        logging.info("Test PASSED: Final splits have the correct number of samples.")
        
        # Check shapes
        logging.info(f"Train TEC shape: {final_data['train']['tec'].shape}")
        logging.info(f"Val TEC shape: {final_data['val']['tec'].shape}")
        logging.info(f"Test TEC shape: {final_data['test']['tec'].shape}")
    else:
        logging.error("Test FAILED: Master script failed to produce data.")

    logging.info("--- Master Test Finished ---") 


================================================
FILE: src/data/dataset.py
================================================
import torch
import numpy as np
import logging
import os
from torch.utils.data import Dataset

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class SlidingWindowSamplerDataset(Dataset):
    """
    A PyTorch Dataset that uses pre-processed, pre-aligned data chunks and
    implements sliding window sampling.
    It loads an entire data split (e.g., 'train') into memory.
    """
    def __init__(self, data_path: str, mode: str, L_in: int = 336, L_out: int = 12):
        """
        Args:
            data_path (str): Path to the directory containing the processed .pt files.
            mode (str): The dataset split to load, one of ['train', 'val', 'test'].
            L_in (int): Input sequence length (window size).
            L_out (int): Output sequence length (prediction horizon).
        """
        super().__init__()
        assert mode in ['train', 'val', 'test'], "Mode must be one of 'train', 'val', or 'test'"
        
        self.L_in = L_in
        self.L_out = L_out
        
        # --- Load pre-processed data from .pt file ---
        file_path = os.path.join(data_path, f"{mode}_set.pt")
        logging.info(f"Attempting to load pre-processed data from: {file_path}")
        try:
            data = torch.load(file_path, map_location='cpu')
            self.X = data['X']
            self.Y = data['Y']
            self.time_features = data['time_features']
            logging.info(f"Successfully loaded '{mode}' data. Shapes: X={self.X.shape}, Y={self.Y.shape}, time_features={self.time_features.shape}")
        except FileNotFoundError:
            logging.error(f"FATAL: Pre-processed data file not found at {file_path}. Please run the preprocessing script first.")
            raise
        
        # The number of samples is determined by how many full windows can be created.
        # Since data is pre-aligned, the logic is simplified.
        self.num_samples = max(0, len(self.X) - self.L_in + 1)
        
        if self.num_samples <= 0:
            logging.warning(f"Insufficient data for windowing: Total length={len(self.X)}, L_in={self.L_in}. Resulting samples: {self.num_samples}")
            self.num_samples = 0
            
        logging.info(f"Dataset '{mode}' initialized. Window config: L_in={self.L_in}, L_out={self.L_out}")
        logging.info(f"Total available samples: {self.num_samples}")

    def __len__(self) -> int:
        """Returns the total number of samples in the dataset."""
        return self.num_samples

    def __getitem__(self, idx: int) -> dict:
        """
        Retrieves a single sample from the dataset. This is now a lightweight slicing operation.
        
        Args:
            idx (int): The index of the sample to retrieve.
            
        Returns:
            dict: A dictionary containing the input window, target window, and time features as tensors.
        """
        if idx >= self.num_samples:
            raise IndexError(f"Index {idx} is out of bounds for a dataset of size {self.num_samples}")

        # Determine the slice for the input window
        x_start = idx
        x_end = idx + self.L_in
        
        # Slice the data tensors directly
        x_window = self.X[x_start:x_end]
        time_features_window = self.time_features[x_start:x_end]
        
        # The target `Y` was pre-calculated and aligned. Y[t] contains future values for X[t].
        # Our input window ends at time `t = idx + L_in - 1`.
        # The corresponding target is at this same index.
        target_y = self.Y[idx + self.L_in - 1]

        # 直接返回，不再有任何scaler操作
        return {
            'x': x_window,      # 已是tensor且已标准化
            'y': target_y,      # 已是tensor且已标准化
            'x_time_features': time_features_window  # 已是tensor
        } 


================================================
FILE: src/evaluation/metrics.py
================================================
import numpy as np
import logging
import joblib
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from scipy.stats import pearsonr

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def evaluate_metrics(y_true_scaled: np.ndarray, y_pred_scaled: np.ndarray, scaler) -> dict:
    """
    Calculates multiple evaluation metrics by first applying inverse transform to scaled data.
    
    Args:
        y_true_scaled (np.ndarray): The ground truth values, scaled.
        y_pred_scaled (np.ndarray): The predicted values, scaled.
        scaler: The fitted scaler for inverse transformation.
        
    Returns:
        dict: A dictionary of calculated metrics.
    """
    # 在计算指标前，先进行逆变换
    original_true_shape = y_true_scaled.shape
    original_pred_shape = y_pred_scaled.shape
    
    # Reshape for scaler (expects 2D input)
    y_true_reshaped = y_true_scaled.reshape(-1, 1)
    y_pred_reshaped = y_pred_scaled.reshape(-1, 1)
    
    # Apply inverse transform
    y_true_unscaled_reshaped = scaler.inverse_transform(y_true_reshaped)
    y_pred_unscaled_reshaped = scaler.inverse_transform(y_pred_reshaped)
    
    # Reshape back to original dimensions
    y_true_unscaled = y_true_unscaled_reshaped.reshape(original_true_shape)
    y_pred_unscaled = y_pred_unscaled_reshaped.reshape(original_pred_shape)

    if y_true_unscaled.ndim > 2: # Reshape if data is in (B, L, N, C) format
        y_true_unscaled = y_true_unscaled.reshape(-1, y_true_unscaled.shape[-1])
        y_pred_unscaled = y_pred_unscaled.reshape(-1, y_pred_unscaled.shape[-1])

    # 后续所有计算都在unscaled值上进行
    mae = mean_absolute_error(y_true_unscaled, y_pred_unscaled)
    rmse = np.sqrt(mean_squared_error(y_true_unscaled, y_pred_unscaled))
    r2 = r2_score(y_true_unscaled, y_pred_unscaled)
    
    # Pearson R needs to be calculated for each feature/horizon and then averaged
    # Handle case where predictions or true values are constant
    pearson_coeffs = []
    for i in range(y_true_unscaled.shape[1]):
        if np.std(y_true_unscaled[:, i]) > 0 and np.std(y_pred_unscaled[:, i]) > 0:
            pearson_coeffs.append(pearsonr(y_true_unscaled[:, i], y_pred_unscaled[:, i])[0])
        else:
            pearson_coeffs.append(0.0) # Or np.nan, depending on desired handling
    
    avg_pearson_r = np.mean(pearson_coeffs)


    metrics = {
        'mae': mae,
        'rmse': rmse,
        'r2_score': r2,
        'pearson_r': avg_pearson_r
    }
    
    return metrics

def evaluate_metrics_unscaled_fallback(y_true_unscaled: np.ndarray, y_pred_unscaled: np.ndarray) -> dict:
    """
    Fallback function for calculating metrics on already unscaled data (backward compatibility).
    """
    if y_true_unscaled.ndim > 2:
        y_true_unscaled = y_true_unscaled.reshape(-1, y_true_unscaled.shape[-1])
        y_pred_unscaled = y_pred_unscaled.reshape(-1, y_pred_unscaled.shape[-1])

    mae = mean_absolute_error(y_true_unscaled, y_pred_unscaled)
    rmse = np.sqrt(mean_squared_error(y_true_unscaled, y_pred_unscaled))
    r2 = r2_score(y_true_unscaled, y_pred_unscaled)
    
    pearson_coeffs = []
    for i in range(y_true_unscaled.shape[1]):
        if np.std(y_true_unscaled[:, i]) > 0 and np.std(y_pred_unscaled[:, i]) > 0:
            pearson_coeffs.append(pearsonr(y_true_unscaled[:, i], y_pred_unscaled[:, i])[0])
        else:
            pearson_coeffs.append(0.0)
    
    avg_pearson_r = np.mean(pearson_coeffs)
    
    return {
        'mae': mae,
        'rmse': rmse,
        'r2_score': r2,
        'pearson_r': avg_pearson_r
    }

def evaluate_horizons(y_true_horizons_scaled: np.ndarray, y_pred_horizons_scaled: np.ndarray, target_scaler_path: str = None) -> dict:
    """
    Evaluates metrics across multiple prediction horizons by first applying inverse transform.
    
    Args:
        y_true_horizons_scaled (np.ndarray): Ground truth of shape (N, L_out, ...), scaled.
        y_pred_horizons_scaled (np.ndarray): Predictions of shape (N, L_out, ...), scaled.
        target_scaler_path (str): Path to the target scaler for inverse transformation.
        
    Returns:
        dict: A dictionary of average metrics across all horizons.
    """
    logging.info("Evaluating metrics across all horizons...")

    # Load scaler if provided
    if target_scaler_path:
        scaler = joblib.load(target_scaler_path)
        logging.info(f"Loaded target scaler from {target_scaler_path}")
    else:
        logging.warning("No target_scaler_path provided, assuming data is already unscaled")
        scaler = None

    all_horizon_metrics = []
    num_horizons = y_true_horizons_scaled.shape[1]

    for i in range(num_horizons):
        y_true_h = y_true_horizons_scaled[:, i]
        y_pred_h = y_pred_horizons_scaled[:, i]
        
        # Use the wrapper for a single horizon, passing scaler
        if scaler is not None:
            horizon_metrics = evaluate_metrics(y_true_h, y_pred_h, scaler)
        else:
            # Fallback to old behavior if no scaler provided (for backward compatibility)
            horizon_metrics = evaluate_metrics_unscaled_fallback(y_true_h, y_pred_h)
        all_horizon_metrics.append(horizon_metrics)
        
    # Calculate and report average metrics
    avg_metrics = {
        'mae_avg': np.mean([m['mae'] for m in all_horizon_metrics]),
        'rmse_avg': np.mean([m['rmse'] for m in all_horizon_metrics]),
        'r2_score_avg': np.mean([m['r2_score'] for m in all_horizon_metrics]),
        'pearson_r_avg': np.mean([m['pearson_r'] for m in all_horizon_metrics]),
        # Add detailed metrics by horizon
        'mae_by_horizon': [m['mae'] for m in all_horizon_metrics],
        'rmse_by_horizon': [m['rmse'] for m in all_horizon_metrics],
        'r2_by_horizon': [m['r2_score'] for m in all_horizon_metrics],
        'pearson_by_horizon': [m['pearson_r'] for m in all_horizon_metrics]
    }
    
    logging.info(f"Average metrics: {avg_metrics}")
    return avg_metrics 


================================================
FILE: src/features/feature_engineering.py
================================================
import numpy as np
import pandas as pd
import logging
from src.data.data_loader import load_and_split_data
import joblib
from sklearn.preprocessing import StandardScaler

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_scaled_space_weather_indices(data: dict) -> dict:
    """
    Extracts and scales space weather indices from the loaded data dictionary.
    """
    logging.info("Extracting and scaling space weather indices...")
    if 'space_weather_indices' not in data:
        logging.error("'space_weather_indices' not found in data.")
        return {}
    indices_data = data['space_weather_indices']
    index_names = ['AE_Index', 'Dst_Index', 'F107_Index', 'Kp_Index', 'ap_Index']
    scaled_indices = {}
    for i, name in enumerate(index_names):
        scale_factor = 1.0
        scaled_indices[name] = indices_data[:, i] * scale_factor
    return scaled_indices

def broadcast_indices(scaled_indices: dict, target_shape: tuple) -> dict:
    """
    Broadcasts the 1D space weather indices to match the spatial dimensions of the TEC data.
    """
    logging.info(f"Broadcasting indices to target shape {target_shape}...")
    broadcasted_indices = {}
    for name, index_array in scaled_indices.items():
        reshaped_array = index_array[:, np.newaxis, np.newaxis]
        broadcasted_indices[name] = np.broadcast_to(reshaped_array, (index_array.shape[0],) + target_shape)
    return broadcasted_indices

def construct_feature_tensor_X(tec_data: np.ndarray, broadcasted_indices: dict) -> np.ndarray:
    """
    Constructs the feature tensor X by stacking TEC data and broadcasted indices.
    """
    logging.info("Constructing feature tensor X...")
    index_names = ['AE_Index', 'Dst_Index', 'F107_Index', 'Kp_Index', 'ap_Index']
    arrays_to_stack = [tec_data[..., np.newaxis]]
    for name in index_names:
        if name in broadcasted_indices:
            arrays_to_stack.append(broadcasted_indices[name][..., np.newaxis])
        else:
            logging.error(f"Index '{name}' not found in broadcasted indices. Aborting.")
            return np.array([])
    feature_tensor_X = np.concatenate(arrays_to_stack, axis=-1)
    logging.info(f"Constructed feature tensor X with shape: {feature_tensor_X.shape}")
    return feature_tensor_X

def construct_target_tensor_Y(tec_data: np.ndarray, horizon: int = 12) -> np.ndarray:
    """
    Constructs the multi-step target tensor Y using a sliding window.
    """
    logging.info(f"Constructing target tensor Y with horizon {horizon}...")
    num_samples = tec_data.shape[0]
    num_targets = num_samples - horizon
    target_tensor_Y = np.zeros((num_targets, tec_data.shape[1], tec_data.shape[2], horizon))
    for i in range(num_targets):
        target_slice = tec_data[i+1 : i+1+horizon]
        target_tensor_Y[i] = target_slice.transpose(1, 2, 0)
    logging.info(f"Constructed target tensor Y with shape: {target_tensor_Y.shape}")
    return target_tensor_Y

def extract_time_features(time_data: pd.DatetimeIndex) -> np.ndarray:
    """
    Extract time features from datetime data.
    
    Args:
        time_data (pd.DatetimeIndex): Datetime index array
        
    Returns:
        np.ndarray: Array of shape (N, 2) containing [time_of_day_slot, day_of_year]
                   where time_of_day_slot is 0-11 for 2-hour intervals (0,2,4,...,22 hours)
    """
    # Convert hour to 2-hour time slot indices (0-11)
    # Hours 0,2,4,6,8,10,12,14,16,18,20,22 -> slots 0,1,2,3,4,5,6,7,8,9,10,11
    time_of_day_slot = time_data.hour // 2
    
    # Day of year (1-366) -> convert to 0-365 for embedding
    day_of_year = time_data.dayofyear - 1
    
    # Stack into (N, 2) array
    time_features = np.stack([time_of_day_slot, day_of_year], axis=-1)
    logging.info(f"Extracted time features with shape: {time_features.shape}")
    logging.info(f"Time slot range: {time_of_day_slot.min()}-{time_of_day_slot.max()}")
    logging.info(f"Day of year range: {day_of_year.min()}-{day_of_year.max()}")
    
    return time_features

def create_features_and_targets(file_paths: list, horizon: int = 12) -> dict:
    """
    Main function to perform feature and target engineering for a given data split.
    """
    logging.info("Starting feature and target engineering process...")
    data_splits = load_and_split_data(file_paths)
    if not data_splits:
        logging.error("Could not load data. Aborting feature engineering.")
        return {}
    processed_splits = {}
    for split_name, data in data_splits.items():
        logging.info(f"--- Processing split: {split_name} ---")
        scaled_indices = get_scaled_space_weather_indices(data)
        broadcasted = broadcast_indices(scaled_indices, data['tec'].shape[1:])
        feature_tensor_X = construct_feature_tensor_X(data['tec'], broadcasted)
        target_tensor_Y = construct_target_tensor_Y(data['tec'], horizon)
        
        # Extract real time features from datetime data
        time_features = extract_time_features(data['time'])
        
        num_targets = target_tensor_Y.shape[0]
        aligned_X = feature_tensor_X[:num_targets]
        aligned_time_features = time_features[:num_targets]
        
        logging.info(f"Aligned X shape: {aligned_X.shape}, Aligned Y shape: {target_tensor_Y.shape}")
        logging.info(f"Aligned time features shape: {aligned_time_features.shape}")
        
        processed_splits[split_name] = {
            'X': aligned_X, 
            'Y': target_tensor_Y,
            'time_features': aligned_time_features
        }
    logging.info("Feature and target engineering completed.")
    return processed_splits

def standardize_features(processed_splits: dict, scaler_path: str = 'data/processed/scaler.joblib') -> (dict, StandardScaler):
    """
    Standardizes the feature tensor X for all data splits.
    
    Args:
        processed_splits (dict): The dictionary containing 'X' and 'Y' for each split.
        scaler_path (str): Path to save the fitted scaler.
        
    Returns:
        tuple: A tuple containing:
            - dict: The dictionary with standardized 'X' tensors.
            - StandardScaler: The fitted scaler object.
    """
    logging.info("Standardizing features...")
    
    X_train = processed_splits['train']['X']
    original_shape_train = X_train.shape
    
    # Subtask 4.1: Reshape for scaler
    X_train_reshaped = X_train.reshape(-1, original_shape_train[-1])
    logging.info(f"Reshaped X_train for scaler: {X_train_reshaped.shape}")
    
    # Subtask 4.2: Fit scaler
    scaler = StandardScaler()
    scaler.fit(X_train_reshaped)
    logging.info("StandardScaler fitted on training data.")
    
    # Subtask 4.4: Save the scaler
    import os
    os.makedirs(os.path.dirname(scaler_path), exist_ok=True)
    joblib.dump(scaler, scaler_path)
    logging.info(f"Scaler saved to {scaler_path}")
    
    # Subtask 4.3: Apply transformation
    standardized_splits = {}
    for split_name, data in processed_splits.items():
        X = data['X']
        original_shape = X.shape
        X_reshaped = X.reshape(-1, original_shape[-1])
        
        X_scaled_reshaped = scaler.transform(X_reshaped)
        
        # Reshape back to original dimensions
        X_scaled = X_scaled_reshaped.reshape(original_shape)
        
        standardized_splits[split_name] = {'X': X_scaled, 'Y': data['Y']}
        logging.info(f"Standardized '{split_name}' X with shape {X_scaled.shape}")
        
    return standardized_splits, scaler

if __name__ == '__main__':
    logging.info("--- Running Test for Feature Engineering (Task 3) ---")
    files = [
        'data/raw/CRIM_SW2hr_AI_v1.2_2014_DataDrivenRange_CN.hdf5',
        'data/raw/CRIM_SW2hr_AI_v1.2_2015_DataDrivenRange_CN.hdf5'
    ]
    processed_data = create_features_and_targets(files)
    if processed_data:
        logging.info("Test PASSED: Main feature engineering function ran successfully.")
        train_X = processed_data['train']['X']
        train_Y = processed_data['train']['Y']
        assert train_X.shape[0] == train_Y.shape[0], "Train X and Y are not aligned."
        assert train_X.shape[3] == 6, "Train X has incorrect number of features."
        assert train_Y.shape[3] == 12, "Train Y has incorrect horizon."
        logging.info("Test PASSED: Train split shapes and alignment are correct.")
        original_data = load_and_split_data(files)['train']['tec']
        t = 0
        i = 5
        assert np.array_equal(train_Y[t, ..., i], original_data[t+i+1]), "Target tensor Y values are incorrect."
        logging.info("Test PASSED: Target tensor Y values are correct.")
    else:
        logging.error("Test FAILED: Feature engineering process failed.")
    logging.info("--- Feature Engineering Test Finished ---")



================================================
FILE: src/graph/graph_constructor.py
================================================
import numpy as np
import logging
import torch
from scipy.sparse import coo_matrix, diags
from sklearn.metrics.pairwise import haversine_distances
from math import radians
import os

# Add an import to the data loader we just created
from src.data.data_loader import load_and_split_data

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_coordinates_from_data(file_paths: list):
    """
    Loads data and extracts the latitude and longitude arrays.
    """
    data = load_and_split_data(file_paths)
    if not data:
        logging.error("Failed to load data to get coordinates.")
        return None, None
    
    # Coordinates are static, so we can take them from any split (e.g., train)
    if 'latitude' in data['train'] and 'longitude' in data['train']:
        lat = data['train']['latitude']
        lon = data['train']['longitude']
        logging.info(f"Successfully loaded coordinates. Latitude shape: {lat.shape}, Longitude shape: {lon.shape}")
        return lat, lon
    else:
        logging.error("Latitude or Longitude not found in the loaded data.")
        return None, None

def calculate_haversine_distance_matrix(lat: np.ndarray, lon: np.ndarray) -> np.ndarray:
    """
    Calculates the pairwise Haversine distance matrix for a grid of coordinates.

    Args:
        lat (np.ndarray): 1D array of latitudes.
        lon (np.ndarray): 1D array of longitudes.

    Returns:
        np.ndarray: A 2D matrix of pairwise distances in kilometers.
    """
    # Create a meshgrid and flatten it to get a list of all coordinate pairs
    lon_grid, lat_grid = np.meshgrid(lon, lat)
    coords = np.vstack([lat_grid.ravel(), lon_grid.ravel()]).T
    
    # Convert degrees to radians for scikit-learn's haversine_distances function
    coords_rad = np.array([[radians(c[0]), radians(c[1])] for c in coords])
    
    # Earth radius in kilometers
    earth_radius_km = 6371.0
    
    logging.info(f"Calculating pairwise Haversine distances for {len(coords)} nodes...")
    distance_matrix = haversine_distances(coords_rad) * earth_radius_km
    logging.info(f"Calculated distance matrix with shape: {distance_matrix.shape}")
    
    return distance_matrix

def construct_binary_adjacency(distance_matrix: np.ndarray, distance_threshold_km: float = 150.0) -> np.ndarray:
    """
    Constructs a binary adjacency matrix based on a distance threshold.

    Args:
        distance_matrix (np.ndarray): The matrix of pairwise distances.
        distance_threshold_km (float): The distance threshold in kilometers.

    Returns:
        np.ndarray: The binary adjacency matrix.
    """
    logging.info(f"Constructing binary adjacency matrix with threshold {distance_threshold_km} km...")
    
    # Create a boolean matrix where True means the distance is within the threshold
    adj_matrix = (distance_matrix <= distance_threshold_km).astype(int)
    
    # Remove self-loops
    np.fill_diagonal(adj_matrix, 0)
    
    logging.info(f"Constructed binary adjacency matrix with {np.sum(adj_matrix)} edges.")
    return adj_matrix

def compute_degree_matrix(adj_matrix: np.ndarray) -> np.ndarray:
    """
    Computes the degree matrix from an adjacency matrix.

    Args:
        adj_matrix (np.ndarray): The binary adjacency matrix.

    Returns:
        np.ndarray: The degree matrix.
    """
    logging.info("Computing degree matrix...")
    degree_vector = np.sum(adj_matrix, axis=1)
    degree_matrix = np.diag(degree_vector)
    logging.info("Degree matrix computed.")
    return degree_matrix

def symmetrically_normalize_adjacency(adj_matrix: np.ndarray) -> coo_matrix:
    """
    Symmetrically normalizes the adjacency matrix.

    Args:
        adj_matrix (np.ndarray): The binary adjacency matrix.

    Returns:
        scipy.sparse.coo_matrix: The normalized sparse adjacency matrix.
    """
    logging.info("Symmetrically normalizing adjacency matrix...")
    
    # Using scipy.sparse for efficient matrix operations
    adj_sparse = coo_matrix(adj_matrix)
    
    # Compute D^(-1/2)
    degree_vector = np.array(adj_sparse.sum(axis=1)).flatten()
    # To avoid division by zero, we add a small epsilon where degree is 0,
    # then the inverse sqrt will be 0 anyway for those nodes.
    with np.errstate(divide='ignore'):
        inv_sqrt_degree_vector = 1.0 / np.sqrt(degree_vector)
    inv_sqrt_degree_vector[np.isinf(inv_sqrt_degree_vector)] = 0
    
    inv_sqrt_degree_matrix = diags(inv_sqrt_degree_vector)
    
    # D^(-1/2) * A * D^(-1/2)
    normalized_adj = inv_sqrt_degree_matrix.dot(adj_sparse).dot(inv_sqrt_degree_matrix)
    
    logging.info("Normalization complete.")
    return normalized_adj.tocoo() # Return in COO format for easy edge extraction

def convert_to_pyg_and_save(normalized_adj: coo_matrix, output_path: str):
    """
    Converts a sparse adjacency matrix to PyTorch Geometric format and saves it.

    Args:
        normalized_adj (coo_matrix): The normalized sparse adjacency matrix.
        output_path (str): The path to save the output .pt file.
    """
    logging.info(f"Converting to PyG format and saving to {output_path}...")
    
    # Extract rows and columns for edge_index
    edge_index = torch.tensor(np.vstack((normalized_adj.row, normalized_adj.col)), dtype=torch.long)
    
    # The data attribute of the COO matrix contains the edge weights
    edge_weight = torch.tensor(normalized_adj.data, dtype=torch.float)
    
    # Save the tensors to a file
    torch.save({'edge_index': edge_index, 'edge_weight': edge_weight}, output_path)
    
    logging.info(f"Graph data saved successfully. Edges: {edge_index.shape[1]}")

if __name__ == '__main__':
    logging.info("--- Running Test for Graph Constructor (Full Task 2) ---")
    
    # Define file paths
    files = [
        'data/raw/CRIM_SW2hr_AI_v1.2_2014_DataDrivenRange_CN.hdf5',
        'data/raw/CRIM_SW2hr_AI_v1.2_2015_DataDrivenRange_CN.hdf5'
    ]

    # 1. Get Coordinates
    latitude, longitude = get_coordinates_from_data(files)

    if latitude is not None and longitude is not None:
        # 2. Calculate Distance Matrix
        dist_matrix = calculate_haversine_distance_matrix(latitude, longitude)
        
        # Verification
        num_nodes = 41 * 71
        assert dist_matrix.shape == (num_nodes, num_nodes), "Distance matrix shape is incorrect."
        logging.info("Test PASSED: Distance matrix shape is correct.")
        
        # Check for symmetry (with a small tolerance for floating point errors)
        assert np.allclose(dist_matrix, dist_matrix.T), "Distance matrix is not symmetric."
        logging.info("Test PASSED: Distance matrix is symmetric.")

        # Check for zero diagonal
        assert np.all(np.diag(dist_matrix) == 0), "Distance matrix diagonal is not all zeros."
        logging.info("Test PASSED: Distance matrix has a zero diagonal.")
        
        # 3. Construct Binary Adjacency Matrix
        adj_matrix = construct_binary_adjacency(dist_matrix)

        # Verification for 2.2
        assert adj_matrix.shape == dist_matrix.shape, "Adjacency matrix shape is incorrect."
        assert np.all((adj_matrix == 0) | (adj_matrix == 1)), "Adjacency matrix is not binary."
        assert np.all(np.diag(adj_matrix) == 0), "Adjacency matrix diagonal is not all zeros (self-loops exist)."
        logging.info("Test PASSED: Adjacency matrix is binary with no self-loops.")

        # 4. Compute Degree Matrix
        degree_matrix = compute_degree_matrix(adj_matrix)

        # Verification for 2.3
        assert degree_matrix.shape == adj_matrix.shape, "Degree matrix shape is incorrect."
        assert np.all(degree_matrix.diagonal() == np.sum(adj_matrix, axis=1)), "Degree matrix diagonals are not correct."
        # Check that off-diagonal elements are zero
        assert np.all(degree_matrix - np.diag(np.diag(degree_matrix)) == 0), "Degree matrix is not diagonal."
        logging.info("Test PASSED: Degree matrix is correct.")

        # 5. Symmetrically Normalize Adjacency Matrix
        normalized_adj_matrix = symmetrically_normalize_adjacency(adj_matrix)

        # Verification for 2.4
        assert normalized_adj_matrix.shape == adj_matrix.shape, "Normalized matrix shape is incorrect."
        # Check that the matrix is still symmetric
        assert np.allclose(normalized_adj_matrix.toarray(), normalized_adj_matrix.toarray().T), "Normalized matrix is not symmetric."
        # Check that values are between 0 and 1
        assert normalized_adj_matrix.min() >= 0 and normalized_adj_matrix.max() <= 1, "Normalized matrix values are out of [0, 1] range."
        logging.info("Test PASSED: Adjacency matrix normalized successfully.")

        # 6. Convert to PyG and Save
        output_dir = 'data/processed'
        os.makedirs(output_dir, exist_ok=True)
        output_file_path = os.path.join(output_dir, 'graph_A.pt')
        convert_to_pyg_and_save(normalized_adj_matrix, output_file_path)

        # Verification for 2.5
        assert os.path.exists(output_file_path), "Output file was not created."
        loaded_graph = torch.load(output_file_path)
        assert 'edge_index' in loaded_graph, "Loaded graph missing 'edge_index'."
        assert 'edge_weight' in loaded_graph, "Loaded graph missing 'edge_weight'."
        assert loaded_graph['edge_index'].shape[0] == 2, "Loaded edge_index has incorrect shape."
        assert loaded_graph['edge_index'].shape[1] == normalized_adj_matrix.nnz, "Number of edges is incorrect."
        assert loaded_graph['edge_weight'].shape[0] == normalized_adj_matrix.nnz, "Number of edge weights is incorrect."
        logging.info("Test PASSED: Graph data saved and loaded correctly.")

        logging.info("--- Graph Constructor Test (Full Task 2) Finished ---")
    else:
        logging.error("Test FAILED: Could not retrieve coordinates.") 


================================================
FILE: src/model/modules.py
================================================
import torch
import torch.nn as nn
import logging
import numpy as np
from einops import rearrange
from transformers import AutoModel
from peft import get_peft_model, LoraConfig
from torch_geometric.nn import GATv2Conv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class Multi_Scale_Conv_Block(nn.Module):
    """
    A building block for multi-scale 1D convolutions.
    It applies multiple parallel convolutions with different kernel sizes,
    concatenates their outputs, and then passes them through a final convolution.
    """
    def __init__(self, in_channels: int, out_channels: int, stride: int, kernel_sizes: list = [3, 5, 7]):
        super().__init__()
        
        self.convs = nn.ModuleList([
            nn.Sequential(
                nn.Conv1d(in_channels, out_channels, kernel_size=k, padding=(k - 1) // 2),
                nn.BatchNorm1d(out_channels),
                nn.GELU()
            ) for k in kernel_sizes
        ])
        
        # The final convolution takes the concatenated output of all parallel convolutions
        # and applies the specified stride.
        self.final_conv = nn.Conv1d(
            in_channels=out_channels * len(kernel_sizes), 
            out_channels=out_channels, 
            kernel_size=1, 
            stride=stride
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x (torch.Tensor): Input tensor of shape (B, C_in, L_in)
        
        Returns:
            torch.Tensor: Output tensor of shape (B, C_out, L_out)
        """
        # Apply parallel convolutions
        conv_outputs = [conv(x) for conv in self.convs]
        
        # Concatenate along the channel dimension
        concatenated = torch.cat(conv_outputs, dim=1)
        
        # Apply final convolution
        output = self.final_conv(concatenated)
        
        return output

class MultiScaleConvEmbedder(nn.Module):
    """
    A multi-scale convolutional embedder that stacks multiple Multi_Scale_Conv_Block
    to downsample a time series.
    """
    def __init__(self, in_channels: int, channel_list: list, strides: list):
        super().__init__()
        
        assert len(channel_list) == len(strides), "Channel list and strides list must have the same length."
        
        layers = []
        current_channels = in_channels
        for i, (out_channels, stride) in enumerate(zip(channel_list, strides)):
            layers.append(Multi_Scale_Conv_Block(current_channels, out_channels, stride))
            current_channels = out_channels
        
        self.embedder = nn.Sequential(*layers)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x (torch.Tensor): Input tensor of shape (B, C_in, L_in)
        
        Returns:
            torch.Tensor: Output tensor of shape (B, C_out, L_out)
        """
        return self.embedder(x)

class LatentPatchingProjection(nn.Module):
    """
    Reshapes the latent sequence from the convolutional embedder into patches
    and projects them to the LLM's hidden dimension.
    """
    def __init__(self, latent_dim: int, patch_len: int, d_llm: int):
        super().__init__()
        self.patch_len = patch_len
        self.projection = nn.Linear(patch_len * latent_dim, d_llm)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x (torch.Tensor): Input tensor of shape (B, L, D_latent)
                              Note: The input from conv embedder is (B, D_latent, L),
                              so it must be permuted before calling this module.
        
        Returns:
            torch.Tensor: Output tensor of shape (B, num_patches, d_llm)
        """
        # b: batch size, p: number of patches, l: patch length, d: latent dimension
        # The rearrange pattern 'b (p l) d -> b p (l d)' does the following:
        # 1. Divides the sequence of length L into p patches of length l.
        # 2. For each patch, it flattens the patch length and latent dimensions together.
        x = rearrange(x, 'b (p l) d -> b p (l d)', l=self.patch_len)
        
        # Project the flattened patch dimension to the LLM's dimension
        x = self.projection(x)
        
        return x

class TemporalEncoder(nn.Module):
    """
    The complete TemporalEncoder module which combines multi-scale convolutions
    and latent patching to prepare time-series data for an LLM.
    """
    def __init__(self, in_channels: int, channel_list: list, strides: list, patch_len: int, d_llm: int):
        super().__init__()
        self.conv_embedder = MultiScaleConvEmbedder(in_channels, channel_list, strides)
        
        # The latent dimension is the output channel count of the last conv block
        latent_dim = channel_list[-1]
        self.patcher = LatentPatchingProjection(latent_dim, patch_len, d_llm)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x (torch.Tensor): Input tensor of shape (B, L_in, C_in)
        
        Returns:
            torch.Tensor: Output tensor of shape (B, num_patches, d_llm)
        """
        # Permute from (B, L, C) to (B, C, L) for Conv1d
        x = x.permute(0, 2, 1)
        
        # Pass through the convolutional embedder
        x = self.conv_embedder(x)
        
        # Permute back from (B, C_out, L_out) to (B, L_out, C_out) for patching
        x = x.permute(0, 2, 1)
        
        # Pass through the patching and projection layer
        x = self.patcher(x)
        
        return x

class LLMBackbone(nn.Module):
    """
    An LLM backbone using a pre-trained GPT-2, truncated and adapted with LoRA.
    """
    def __init__(self, num_layers_to_keep: int = 3):
        super().__init__()
        
        # Subtask 9.1: Load pre-trained GPT-2 model
        logging.info("Loading pre-trained GPT-2 model...")
        self.model = AutoModel.from_pretrained('gpt2')
        logging.info("GPT-2 model loaded.")

        # Subtask 9.2: Truncate the model
        logging.info(f"Truncating model to keep only the first {num_layers_to_keep} layers.")
        self.model.h = self.model.h[:num_layers_to_keep]
        logging.info(f"Model truncated. Number of layers: {len(self.model.h)}")

        # Subtask 9.3: Define and apply LoRA config
        logging.info("Defining LoRA configuration...")
        lora_config = LoraConfig(
            r=16,
            lora_alpha=32,
            target_modules=["c_attn"], # For GPT-2, attention weights are in 'c_attn'
            lora_dropout=0.1,
            bias="none"
        )
        logging.info("Applying LoRA to the model...")
        self.model = get_peft_model(self.model, lora_config)
        logging.info("LoRA applied successfully.")
        
        # Subtask 9.4: Freeze parameters selectively
        self._freeze_parameters()
        
        logging.info("Trainable parameters after freezing:")
        self.model.print_trainable_parameters()

    def _freeze_parameters(self):
        logging.info("Freezing all parameters initially...")
        for param in self.model.parameters():
            param.requires_grad = False
        
        logging.info("Unfreezing LoRA, LayerNorm, and positional encoding parameters...")
        for name, param in self.model.named_parameters():
            if 'lora_' in name or 'ln_' in name or 'wpe' in name:
                param.requires_grad = True

    def forward(self, inputs_embeds: torch.Tensor, attention_mask: torch.Tensor) -> torch.Tensor:
        # This will be fully implemented in a later subtask
        # Note: We now use inputs_embeds instead of input_ids
        outputs = self.model(inputs_embeds=inputs_embeds, attention_mask=attention_mask)
        return outputs.last_hidden_state

class SpatioTemporalEmbedding(nn.Module):
    """
    Creates learnable embeddings for nodes, time of day, and day of year.
    """
    def __init__(self, d_emb: int, num_nodes: int = 2911):
        super().__init__()
        
        self.d_emb = d_emb
        # Subtask 6.2: Initialize Embedding Layers
        self.node_embedding = nn.Embedding(num_embeddings=num_nodes, embedding_dim=d_emb)
        self.tod_embedding = nn.Embedding(num_embeddings=12, embedding_dim=d_emb) # 12 time slots (2-hour intervals)
        self.doy_embedding = nn.Embedding(num_embeddings=366, embedding_dim=d_emb) # 366 for leap years
        
        logging.info("SpatioTemporalEmbedding module initialized.")

    def forward(self, x: torch.Tensor, time_features: torch.Tensor) -> torch.Tensor:
        """
        Adds spatio-temporal embeddings to the input tensor.
        
        Args:
            x (torch.Tensor): Input tensor of shape (B, L, N, C_in).
            time_features (torch.Tensor): Time features of shape (B, L, N, 2) 
                                          containing (hour_of_day, day_of_year).
                                          
        Returns:
            torch.Tensor: Output tensor with embeddings added, shape (B, L, N, C_in).
        """
        batch_size, seq_len, num_nodes, _ = x.shape
        
        # Subtask 6.3: Perform embedding lookups
        # Node embeddings: one for each node, repeat across batch and time
        node_ids = torch.arange(num_nodes, device=x.device)
        node_emb = self.node_embedding(node_ids) # (N, d_emb)
        
        # TOD and DOY embeddings
        tod_indices = time_features[..., 0].long() # (B, L, N)
        doy_indices = time_features[..., 1].long() # (B, L, N)
        tod_emb = self.tod_embedding(tod_indices) # (B, L, N, d_emb)
        doy_emb = self.doy_embedding(doy_indices) # (B, L, N, d_emb)
        
        # Subtask 6.4: Broadcast and combine
        # Reshape node_emb to be broadcastable
        node_emb = node_emb.view(1, 1, num_nodes, self.d_emb) # (1, 1, N, d_emb)
        
        # Add the embeddings together. Broadcasting handles the dimensions.
        combined_emb = node_emb + tod_emb + doy_emb # (B, L, N, d_emb)
        
        # The input features 'x' and the combined embeddings will be concatenated.
        # To do this, we need to make sure the embedding is expanded to match the batch and seq len of x.
        # Broadcasting already handled this. Now we concatenate along the last dimension.
        
        output = torch.cat([x, combined_emb], dim=-1)
        
        return output

class PredictionHead(nn.Module):
    """
    Maps features from the LLM backbone to the final prediction sequence.
    """
    def __init__(self, input_dim: int, output_dim: int):
        """
        Args:
            input_dim (int): The flattened input dimension from the LLM (e.g., 21 * 768).
            output_dim (int): The output prediction dimension (e.g., 12).
        """
        super().__init__()
        self.fc = nn.Linear(input_dim, output_dim)
        self.activation = nn.ReLU() # Add ReLU activation to ensure non-negative output
        logging.info(f"PredictionHead initialized with input_dim={input_dim}, output_dim={output_dim}")

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Processes the input tensor to produce the final output.
        
        Args:
            x (torch.Tensor): Input tensor of shape (B, seq_len, hidden_size).
            
        Returns:
            torch.Tensor: Output tensor of shape (B, output_dim).
        """
        # Flatten the sequence and hidden dimensions
        batch_size = x.shape[0]
        x = x.view(batch_size, -1)
        
        # Apply the final linear layer and activation
        output = self.fc(x)
        output = self.activation(output)
        return output

class SpatialEncoder(nn.Module):
    """
    Captures spatial dependencies using a GATv2 layer.
    """
    def __init__(self, in_channels: int, out_channels: int, heads: int = 2, dropout: float = 0.1):
        """
        Args:
            in_channels (int): Number of input features for each node.
            out_channels (int): Number of output features for each node.
            heads (int): Number of multi-head attentions.
            dropout (float): Dropout rate.
        """
        super().__init__()
        # Subtask 7.2: Instantiate GATv2Conv Layer
        self.gat_conv = GATv2Conv(
            in_channels, 
            out_channels, 
            heads=heads, 
            dropout=dropout, 
            concat=True, # Concatenates the multi-head attentions
            add_self_loops=True # Recommended for GAT
        )
        self.output_channels = out_channels * heads
        logging.info(f"SpatialEncoder initialized with out_channels={self.output_channels}")

    def forward(self, x: torch.Tensor, edge_index: torch.Tensor) -> torch.Tensor:
        """
        Processes the batched graph data.
        
        Args:
            x (torch.Tensor): Batched node features of shape (B * L, N, C_in).
            edge_index (torch.Tensor): Graph connectivity in COO format.
            
        Returns:
            torch.Tensor: Updated node features of shape (B * L, N, C_out * heads).
        """
        # Subtask 7.3: Reshape input
        batch_size, num_nodes, in_channels = x.shape
        # Reshape from (B*L, N, C) to (B*L*N, C) for PyG layer
        x_reshaped = x.reshape(-1, in_channels)
        
        # Subtask 7.4: Apply GATv2 layer and reshape output
        # The GAT layer operates on the full batch of nodes
        gat_output = self.gat_conv(x_reshaped, edge_index)
        
        # Reshape back to (B*L, N, C_out*heads)
        output = gat_output.view(batch_size, num_nodes, self.output_channels)
        
        return output 


================================================
FILE: src/model/tec_mollm.py
================================================
import torch
import torch.nn as nn
import logging
from .modules import (
    SpatioTemporalEmbedding, 
    SpatialEncoder, 
    TemporalEncoder, 
    LLMBackbone, 
    PredictionHead
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class TEC_MoLLM(nn.Module):
    """
    The main TEC-MoLLM model that assembles all sub-modules.
    """
    def __init__(self, model_config: dict):
        super().__init__()
        
        self.num_nodes = model_config['num_nodes']
        
        # Subtask 11.1: Instantiate all sub-modules
        self.spatio_temporal_embedding = SpatioTemporalEmbedding(
            d_emb=model_config['d_emb'],
            num_nodes=self.num_nodes
        )
        # The input to the spatial encoder is now the original channels + embedding dim
        spatial_in_channels = model_config['spatial_in_channels_base'] + model_config['d_emb']
        self.spatial_encoder = SpatialEncoder(
            in_channels=spatial_in_channels,
            out_channels=model_config['spatial_out_channels'],
            heads=model_config['spatial_heads']
        )
        # The input to the temporal encoder is the output of the spatial encoder
        temporal_in_channels = model_config['spatial_out_channels'] * model_config['spatial_heads']
        self.temporal_encoder = TemporalEncoder(
            in_channels=temporal_in_channels,
            channel_list=model_config['temporal_channel_list'],
            strides=model_config['temporal_strides'],
            patch_len=model_config['patch_len'],
            d_llm=model_config['d_llm']
        )
        self.llm_backbone = LLMBackbone(
            num_layers_to_keep=model_config['llm_layers']
        )
        # Calculate the actual sequence length after convolutions: 24 -> 12 -> 6 (stride 2, stride 2)
        conv_output_len = model_config['temporal_seq_len'] // (model_config['temporal_strides'][0] * model_config['temporal_strides'][1])
        num_patches = conv_output_len // model_config['patch_len']
        self.prediction_head = PredictionHead(
            input_dim=model_config['d_llm'] * num_patches,
            output_dim=model_config['prediction_horizon']
        )
        logging.info("TEC_MoLLM model initialized with all sub-modules.")

    def forward(self, x: torch.Tensor, time_features: torch.Tensor, edge_index: torch.Tensor) -> torch.Tensor:
        """
        Defines the complete forward pass from input data to prediction.
        
        Args:
            x (torch.Tensor): Input features of shape (B, L_in, N, C_in).
            time_features (torch.Tensor): Time features for embedding.
            edge_index (torch.Tensor): Graph connectivity.
            
        Returns:
            torch.Tensor: Final prediction of shape (B, L_out, N, 1).
        """
        B, L_in, N, C_in = x.shape
        
        # 1. SpatioTemporalEmbedding
        x = self.spatio_temporal_embedding(x, time_features)
        
        # 2. SpatialEncoder
        # Reshape for SpatialEncoder: (B, L, N, C) -> (B*L, N, C)
        C_in_with_emb = x.shape[-1]
        x_spatial = x.reshape(-1, N, C_in_with_emb)
        x_spatial = self.spatial_encoder(x_spatial, edge_index)
        
        # 3. TemporalEncoder
        # Reshape for TemporalEncoder: (B*L, N, C_out_spatial) -> (B*N, L, C_out_spatial)
        C_out_spatial = x_spatial.shape[-1]
        x_temporal = x_spatial.reshape(B, L_in, N, C_out_spatial).permute(0, 2, 1, 3)
        x_temporal = x_temporal.reshape(-1, L_in, C_out_spatial)
        x_temporal = self.temporal_encoder(x_temporal) # Output: (B*N, num_patches, d_llm)
        
        # 4. LLMBackbone
        # The LLM doesn't need an attention mask if we pass embeddings directly
        attention_mask = torch.ones(x_temporal.shape[:-1], device=x.device, dtype=torch.long)
        x_llm = self.llm_backbone(inputs_embeds=x_temporal, attention_mask=attention_mask)
        
        # 5. PredictionHead
        predictions = self.prediction_head(x_llm) # Output: (B*N, L_out)
        
        # 6. Final Reshape
        L_out = predictions.shape[-1]
        final_output = predictions.view(B, N, L_out).permute(0, 2, 1).unsqueeze(-1)
        
        return final_output 


================================================
FILE: src/models/baselines.py
================================================
import numpy as np
import logging
import joblib
from statsmodels.tsa.statespace.sarimax import SARIMAX

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class HistoricalAverage:
    def __init__(self):
        self.averages = None

    def fit(self, tec_data: np.ndarray, time_data: np.ndarray):
        """
        Calculates the historical average for each node and time-of-day slot.
        
        Args:
            tec_data (np.ndarray): Shape (N_times, N_nodes).
            time_data (np.ndarray): Corresponding time information with hour.
        """
        logging.info("Fitting Historical Average model...")
        num_nodes = tec_data.shape[1]
        # 2-hour resolution -> 12 slots per day
        self.averages = np.zeros((num_nodes, 12))
        
        hours = (time_data.astype('datetime64[h]').astype(int) % 24).astype(int)
        time_slots = hours // 2
        
        for node in range(num_nodes):
            for slot in range(12):
                mask = time_slots == slot
                self.averages[node, slot] = np.mean(tec_data[mask, node])
        logging.info("HA model fitted.")

    def predict(self, time_data: np.ndarray, num_nodes: int) -> np.ndarray:
        """
        Predicts using the pre-calculated averages.
        """
        hours = (time_data.astype('datetime64[h]').astype(int) % 24).astype(int)
        time_slots = hours // 2
        predictions = np.zeros((len(time_data), num_nodes))
        
        for i, slot in enumerate(time_slots):
            predictions[i, :] = self.averages[:, slot]
        return predictions

class SarimaBaseline:
    def __init__(self, order=(1,1,1), seasonal_order=(1,1,1,12)):
        self.models = {}
        self.order = order
        self.seasonal_order = seasonal_order

    def fit(self, tec_data: np.ndarray, node_indices: list):
        """
        Fits a SARIMA model for each specified node.
        """
        for node_idx in node_indices:
            logging.info(f"Fitting SARIMA for node {node_idx}...")
            time_series = tec_data[:, node_idx]
            model = SARIMAX(time_series, order=self.order, seasonal_order=self.seasonal_order)
            self.models[node_idx] = model.fit(disp=False)
            logging.info(f"SARIMA fitted for node {node_idx}.")

    def predict(self, node_indices: list, steps: int) -> dict:
        """
        Makes predictions for specified nodes.
        """
        predictions = {}
        for node_idx in node_indices:
            if node_idx in self.models:
                predictions[node_idx] = self.models[node_idx].forecast(steps=steps)
        return predictions

def save_baseline(model, path):
    joblib.dump(model, path)
    logging.info(f"Baseline model saved to {path}")

def load_baseline(path):
    logging.info(f"Loading baseline model from {path}")
    return joblib.load(path) 

