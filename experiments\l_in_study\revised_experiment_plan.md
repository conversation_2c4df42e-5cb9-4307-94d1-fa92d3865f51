# 修订版 L_in 调优实验计划

## 实验结果总结

### 已完成实验
- **L_in=48**: ✅ 成功完成
  - MAE: 114.757, RMSE: 200.126, R²: 0.7736, Pearson R: 0.8809
  - batch_size=4, 训练时间1.5小时
  - 性能优秀，作为基准

### 遇到的问题
- **L_in=96**: ❌ 显存不足
  - batch_size=4时训练OOM
  - batch_size=2时训练可行但推理OOM
  - 无法完成完整的性能评估

## 修订策略

基于硬件限制，采用渐进式实验策略：

### 实验组设计
1. **L_in=48** (已完成): 基准性能
2. **L_in=72**: 中等步长，测试性能提升潜力
3. **L_in=84**: 接近极限的测试
4. **分析与总结**: 基于可完成的实验绘制性能曲线

### 技术参数调整
- L_in=72: batch_size=2, accumulation_steps=2 (等效batch_size=4)
- L_in=84: batch_size=1, accumulation_steps=4 (等效batch_size=4) 
- 如果仍有显存问题，降低batch_size并相应调整accumulation_steps

### 预期成果
- 明确在当前硬件条件下的L_in上限
- 量化历史序列长度对预测性能的影响
- 为实际部署提供性能-资源平衡的参考

## 关键发现
更长的历史输入序列虽然理论上能提供更多信息，但在实际应用中需要考虑：
1. 显存占用呈平方级增长
2. 推理时间显著增加
3. 收益递减效应

这正验证了您提到的"性能与资源最佳平衡点"的重要性。