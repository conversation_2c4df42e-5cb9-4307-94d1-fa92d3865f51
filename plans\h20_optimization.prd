### PRD: H20 GPU 性能极致优化

#### 1. 项目概述与核心目标

本项目旨在针对单张NVIDIA H20 (96GB) GPU的顶级硬件环境，对现有的时间序列预测模型训练流程进行端到端的性能优化。核心瓶颈已从GPU算力/显存转移至数据I/O和CPU预处理。

**最终目标：** 通过重构数据流水线和优化训练脚本，将H20 GPU的利用率（GPU Util）稳定在90%以上，最大化硬件潜力，显著缩短模型训练和实验周期。

---

#### 2. 功能需求与任务分解

##### **里程碑 1: 数据预处理流水线重构**

**任务 1.1: 创建离线数据预处理脚本 (`preprocess.py`)**
*   **需求**: 编写一个一次性执行的Python脚本 `preprocess.py`。
*   **功能细节**:
    1.  从 `data/raw/` 目录加载原始的HDF5数据文件。
    2.  执行所有必要的特征工程和标准化操作。
    3.  将处理完成、维度对齐的训练集、验证集、测试集数据（包含 `X`, `Y`, `time_features`）分别保存为3个独立的大型PyTorch张量文件。
    4.  **产物**:
        *   `data/processed/train_set.pt`
        *   `data/processed/validation_set.pt`
        *   `data/processed/test_set.pt`
*   **验收标准**: 脚本成功运行，并在指定目录下生成三个`.pt`文件。

**任务 1.2: 重构PyTorch `Dataset` 类**
*   **需求**: 修改现有的`Dataset`实现，以适配离线处理好的数据。
*   **功能细节**:
    1.  在`__init__`方法中，根据传入的模式（'train', 'val', 'test'），直接使用`torch.load()`将对应的`*.pt`文件（例如 `data/processed/train_set.pt`）一次性完整加载到CPU内存中。
    2.  重写`__getitem__`方法，使其逻辑极其轻量。它唯一的任务就是根据索引`idx`，从已加载在内存中的巨大张量上执行切片操作，返回一个`(X_sample, Y_sample, time_features_sample)`元组。
*   **验收标准**: `Dataset`对象可以被成功实例化，并且`__getitem__`的CPU执行时间显著降低。

##### **里程碑 2: 训练脚本极致优化**

**任务 2.1: 优化`DataLoader`配置**
*   **需求**: 在`train.py`中，调整`DataLoader`的实例化参数以最大化数据吞吐量。
*   **功能细节**:
    *   `num_workers`: 设为 `16`。
    *   `pin_memory`: 设为 `True`。
    *   `prefetch_factor`: 设为 `4`。
*   **验收标准**: `DataLoader`在训练循环中能够持续、高效地向GPU提供数据。

**任务 2.2: 启用混合精度训练与`torch.compile`**
*   **需求**: 在`train.py`的训练循环中，全面启用针对Hopper架构的性能优化特性。
*   **功能细节**:
    1.  引入`torch.cuda.amp.GradScaler`和`autocast`上下文管理器。
    2.  在模型前向传播和损失计算部分，使用`with autocast(dtype=torch.bfloat16):`进行包裹。
    3.  使用`GradScaler`实例对损失进行缩放（`scale`）、反向传播、更新优化器（`step`）和更新缩放器（`update`）。
    4.  在模型移动到device (`.to(device)`) 之后，立即使用`model = torch.compile(model)`对模型进行编译。
*   **验收标准**: 训练能够以`bfloat16`混合精度正确运行，且`torch.compile`无报错。

**任务 2.3: 调整核心超参数以利用大显存**
*   **需求**: 修改`train.py`的默认超参数，以充分利用H20的96GB显存。
*   **功能细节**:
    *   `--L_in` (输入序列长度): 默认值设为 `336`。
    *   `--batch_size` (批次大小): 默认值设为 `64`。
*   **验收标准**: 训练能够以指定的更大输入长度和批次大小启动并稳定运行。

##### **里程碑 3: 验证与性能监控**

**任务 3.1: 执行优化后的训练并进行性能验证**
*   **需求**: 运行完整的训练流程，并监控关键性能指标以确认优化效果。
*   **功能细节**:
    1.  使用优化后的`train.py`脚本启动训练。
    2.  在训练过程中，使用`nvidia-smi dmon`或`gpustat -i`监控GPU。
    3.  使用`htop`或类似工具监控CPU核心的负载情况。
*   **验收标准**:
    *   **首要标准**: GPU利用率（GPU-Util）在训练的大部分时间内稳定在**90%以上**。
    *   显存利用率（Memory-Util）在设定的`batch_size`下处于一个较高但安全（无OOM）的水平。
    *   CPU的16个核心表现出持续的负载，证明`num_workers`在有效工作。
    *   记录下最终达成的稳定训练速度（e.g., iterations/sec）。 